﻿using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Authorization;
using VNPT.Api.Entities;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for DiaBan
    /// </summary>
    [AbpAuthorize(PermissionNames.Pages_DiaBan)]
    public class DiaBanAppService : ApiAppServiceBase
    {
        private readonly IRepository<DiaBan> _DiaBansRepository;

        /// <summary>
        /// Service for DiaBan
        /// </summary>
        public DiaBanAppService(IRepository<DiaBan> DiaBansRepository)
        {
            _DiaBansRepository = DiaBansRepository;
        }

        /// <summary>
        /// Get all DiaBan
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/dia-ban")]
        public async Task<PagedResultDto<GetDiaBanOutput>> GetAll(GetAllDiaBansInput input)
        {
            var filtered = _DiaBansRepository.GetAll()
                        .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false || e.ma.ToLower().Contains(input.keyword.ToLower()) || e.ten.ToLower().Contains(input.keyword.ToLower()))
                        .WhereIf(!string.IsNullOrWhiteSpace(input.trung_tam), e => input.trung_tam == e.trung_tam)
                        .WhereIf(!string.IsNullOrWhiteSpace(input.to_ky_thuat), e => input.to_ky_thuat == e.to_ky_thuat)
                        .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue);

            input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
            var pagedAndFiltered = filtered
                .OrderBy(input.Sorting ?? "id asc")
                .PageBy(input);

            var totalCount = await filtered.CountAsync();
            var results = new List<GetDiaBanOutput>();
            foreach (var item in pagedAndFiltered)
            {
                var output = ObjectMapper.Map<GetDiaBanOutput>(item);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return new PagedResultDto<GetDiaBanOutput>(
                totalCount,
                results
            );

        }

        /// <summary>
        /// Get DiaBan by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/services/app/dia-ban/{id}")]
        public async Task<GetDiaBanOutput> Get(int id)
        {
            var item = await _DiaBansRepository.FirstOrDefaultAsync(e => e.Id == id);
            if (item == null)
            {
                throw new UserFriendlyException("id does not exist");
            }
            var output = ObjectMapper.Map<GetDiaBanOutput>(item);
            output.tinh_trang = item.published_at.HasValue;
            return output;
        }

        /// <summary>
        /// Import DiaBan
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [Route("api/services/app/dia-ban/import")]
        public async Task CreateOrEdit(CreateOrEditDiaBanDto[] list)
        {
            foreach (var item in list)
            {
                var diaBan = await _DiaBansRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.ma == item.ma);
                if (diaBan != null)
                {
                    item.Id = diaBan.Id;
                    await Update(item);
                }
                else
                {
                    await Create(item);
                }
            }
        }

        /// <summary>
        /// Create or Edit DiaBan
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/dia-ban")]
        public async Task CreateOrEdit(CreateOrEditDiaBanDto input)
        {
            if (input.Id == null)
            {
                await Create(input);
            }
            else
            {
                await Update(input);
            }
        }

        /// <summary>
        /// Create DiaBan
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Create(CreateOrEditDiaBanDto input)
        {
            var item = ObjectMapper.Map<DiaBan>(input);
            if (AbpSession.TenantId != null)
            {
                item.TenantId = (int?)AbpSession.TenantId;
            }
            if (AbpSession.UserId != null)
            {
                item.created_by_id = AbpSession.UserId;
            }
            item.created_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            await _DiaBansRepository.InsertAsync(item);
        }

        /// <summary>
        /// Update DiaBan
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Update(CreateOrEditDiaBanDto input)
        {
            var item = await _DiaBansRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == (int)input.Id);
            if (AbpSession.UserId != null)
            {
                item.updated_by_id = AbpSession.UserId;
            }
            item.updated_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            ObjectMapper.Map(input, item);
        }

        /// <summary>
        /// Delete DiaBan
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/dia-ban")]
        // [HttpPost]
        public async Task Delete(EntityDto input)
        {
            await _DiaBansRepository.DeleteAsync(input.Id);
        }

        /// <summary>
        /// Get all DiaBan for table dropdown
        /// </summary>
        /// <returns></returns>
        [Route("api/services/app/dia-ban/list")]
        public async Task<List<DiaBanOutput>> GetAllForTableDropdown()
        {
            return await _DiaBansRepository.GetAll()
                .Where(e => e.published_at.HasValue)
                .OrderBy(e => e.ten)
                .Select(item => new DiaBanOutput
                {
                    id = item.Id,
                    ma = item.ma,
                    ten = item.ten,
                    trung_tam = item.trung_tam,
                    to_ky_thuat = item.to_ky_thuat
                }).ToListAsync();
        }
    }
}