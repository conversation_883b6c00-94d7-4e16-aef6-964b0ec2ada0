﻿using Abp.Domain.Entities;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace VNPT.Api.Entities
{
    [Table("nhan_vien", Schema = "vnpt")]
    public class NhanVien : Entity, IMayHaveTenant
    {
        [Column("id")]
        public override int Id { get; set; }
        [Column("tenant_id")]
        public int? TenantId { get; set; }
        public DateTime? created_at { get; set; }
        public DateTime? updated_at { get; set; }
        public DateTime? published_at { get; set; }
        public long? created_by_id { get; set; }
        public long? updated_by_id { get; set; }
        public string ma { get; set; }
        public string ho_ten { get; set; }
        public LoaiNhanVien? loai { get; set; }
        public string don_vi { get; set; }
    }
    public enum LoaiNhanVien
    {
        KyThuat = 1,
        KinhDoanh = 2
    }
}