﻿using AutoMapper;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Entities;

namespace VNPT.Api
{
    internal static class CustomDtoMapper
    {
        public static void CreateMappings(IMapperConfigurationExpression configuration)
        {
            configuration.CreateMap<CauHinhDto, CauHinh>().ReverseMap();
            configuration.CreateMap<CreateOrEditCauHinhDto, CauHinh>().ReverseMap();
            configuration.CreateMap<GetCauHinhOutput, CauHinh>().ReverseMap();

            configuration.CreateMap<DichVuDto, DichVu>().ReverseMap();
            configuration.CreateMap<CreateOrEditDichVuDto, DichVu>().ReverseMap();
            configuration.CreateMap<GetDichVuOutput, DichVu>().ReverseMap();
            configuration.CreateMap<DichVuOutput, DichVu>().ReverseMap();

            configuration.CreateMap<Ghi<PERSON>huDto, <PERSON>hi<PERSON>hu>().ReverseMap();
            configuration.CreateMap<CreateOrEditGhiChuDto, Ghi<PERSON>hu>().ReverseMap();
            configuration.CreateMap<GetGhiChuOutput, GhiChu>().ReverseMap();

            configuration.CreateMap<NhanVienDto, NhanVien>().ReverseMap();
            configuration.CreateMap<CreateOrEditNhanVienDto, NhanVien>().ReverseMap();
            configuration.CreateMap<GetNhanVienOutput, NhanVien>().ReverseMap();
            configuration.CreateMap<NhanVienOutput, NhanVien>().ReverseMap();

            configuration.CreateMap<ThueBaoDto, ThueBao>().ReverseMap();
            configuration.CreateMap<CreateOrEditThueBaoDto, ThueBao>().ReverseMap();
            configuration.CreateMap<GetThueBaoOutput, ThueBao>().ReverseMap();
            configuration.CreateMap<ThueBaoOutput, ThueBao>().ReverseMap();

            configuration.CreateMap<TrangThaiDto, TrangThai>().ReverseMap();
            configuration.CreateMap<CreateOrEditTrangThaiDto, TrangThai>().ReverseMap();
            configuration.CreateMap<GetTrangThaiOutput, TrangThai>().ReverseMap();
            configuration.CreateMap<TrangThaiOutput, TrangThai>().ReverseMap();

            configuration.CreateMap<TrungTamDto, TrungTam>().ReverseMap();
            configuration.CreateMap<CreateOrEditTrungTamDto, TrungTam>().ReverseMap();
            configuration.CreateMap<GetTrungTamOutput, TrungTam>().ReverseMap();
            configuration.CreateMap<TrungTamOutput, TrungTam>().ReverseMap();

            configuration.CreateMap<ToKyThuatDto, ToKyThuat>().ReverseMap();
            configuration.CreateMap<CreateOrEditToKyThuatDto, ToKyThuat>().ReverseMap();
            configuration.CreateMap<GetToKyThuatOutput, ToKyThuat>().ReverseMap();
            configuration.CreateMap<ToKyThuatOutput, ToKyThuat>().ReverseMap();

            configuration.CreateMap<DiaBanDto, DiaBan>().ReverseMap();
            configuration.CreateMap<CreateOrEditDiaBanDto, DiaBan>().ReverseMap();
            configuration.CreateMap<GetDiaBanOutput, DiaBan>().ReverseMap();
            configuration.CreateMap<DiaBanOutput, DiaBan>().ReverseMap();

            configuration.CreateMap<PhongBanHangDto, PhongBanHang>().ReverseMap();
            configuration.CreateMap<CreateOrEditPhongBanHangDto, PhongBanHang>().ReverseMap();
            configuration.CreateMap<GetPhongBanHangOutput, PhongBanHang>().ReverseMap();
            configuration.CreateMap<PhongBanHangOutput, PhongBanHang>().ReverseMap();

            configuration.CreateMap<TinhDto, Tinh>().ReverseMap();
            configuration.CreateMap<CreateOrEditTinhDto, Tinh>().ReverseMap();
            configuration.CreateMap<GetTinhOutput, Tinh>().ReverseMap();
            configuration.CreateMap<TinhOutput, Tinh>().ReverseMap();

            configuration.CreateMap<HuyenDto, Huyen>().ReverseMap();
            configuration.CreateMap<CreateOrEditHuyenDto, Huyen>().ReverseMap();
            configuration.CreateMap<GetHuyenOutput, Huyen>().ReverseMap();
            configuration.CreateMap<HuyenOutput, Huyen>().ReverseMap();

            configuration.CreateMap<XaDto, Xa>().ReverseMap();
            configuration.CreateMap<CreateOrEditXaDto, Xa>().ReverseMap();
            configuration.CreateMap<GetXaOutput, Xa>().ReverseMap();
            configuration.CreateMap<XaOutput, Xa>().ReverseMap();

            configuration.CreateMap<ApKhuPhoDto, ApKhuPho>().ReverseMap();
            configuration.CreateMap<CreateOrEditApKhuPhoDto, ApKhuPho>().ReverseMap();
            configuration.CreateMap<GetApKhuPhoOutput, ApKhuPho>().ReverseMap();
            configuration.CreateMap<ApKhuPhoOutput, ApKhuPho>().ReverseMap();
        }
    }
}