using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Authorization;
using VNPT.Api.Authorization.Roles;
using VNPT.Api.Authorization.Users;
using VNPT.Api.Entities;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for TrungTam
    /// </summary>
    [AbpAuthorize(PermissionNames.Pages_TrungTam)]
    public class TrungTamAppService : ApiAppServiceBase
    {
        private readonly IRepository<TrungTam> _TrungTamsRepository;
        private readonly UserManager _userManager;

        /// <summary>
        /// Service for TrungTam
        /// </summary>
        public TrungTamAppService(IRepository<TrungTam> TrungTamsRepository, UserManager userManager)
        {
            _TrungTamsRepository = TrungTamsRepository;
            _userManager = userManager;
        }

        /// <summary>
        /// Get all TrungTam
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/trung-tam")]
        public async Task<PagedResultDto<GetTrungTamOutput>> GetAll(GetAllTrungTamsInput input)
        {
            if (AbpSession.UserId == null)
            {
                throw new UserFriendlyException("There is no current user!");
            }
            var currentUser = await _userManager.GetUserByIdAsync(AbpSession.GetUserId());
            var roles = await _userManager.GetRolesAsync(currentUser);
            var isManager = roles.Contains("Lanh_Dao");
            var scope = currentUser.Surname.ToLower();

            var filtered = _TrungTamsRepository.GetAll()
                        .Include(e => e.TinhFk)
                        .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false || e.ma.ToLower().Contains(input.keyword.ToLower()) || e.ten.ToLower().Contains(input.keyword.ToLower()))
                        .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue)
                        .WhereIf(!isManager, e => scope.Contains(e.ma.ToLower()));

            input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
            var pagedAndFiltered = filtered
                .OrderBy(input.Sorting ?? "id asc")
                .PageBy(input);

            var totalCount = await filtered.CountAsync();
            var results = new List<GetTrungTamOutput>();
            foreach (var item in pagedAndFiltered)
            {
                var output = ObjectMapper.Map<GetTrungTamOutput>(item);
                output.tinh = ObjectMapper.Map<TinhOutput>(item.TinhFk);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return new PagedResultDto<GetTrungTamOutput>(
                totalCount,
                results
            );
        }

        /// <summary>
        /// Get TrungTam by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/services/app/trung-tam/{id}")]
        public async Task<GetTrungTamOutput> Get(int id)
        {
            var item = await _TrungTamsRepository
                    .GetAll()
                    .Include(e => e.TinhFk)
                    .FirstOrDefaultAsync(e => e.Id == id);
            if (item == null)
            {
                throw new UserFriendlyException("id does not exist");
            }
            var output = ObjectMapper.Map<GetTrungTamOutput>(item);
            output.tinh = ObjectMapper.Map<TinhOutput>(item.TinhFk);
            output.tinh_trang = item.published_at.HasValue;
            return output;
        }

        /// <summary>
        /// Create or Edit TrungTam
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/trung-tam")]
        public async Task CreateOrEdit(CreateOrEditTrungTamDto input)
        {
            if (input.Id == null)
            {
                await Create(input);
            }
            else
            {
                await Update(input);
            }
        }

        /// <summary>
        /// Create TrungTam
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Create(CreateOrEditTrungTamDto input)
        {
            var item = ObjectMapper.Map<TrungTam>(input);
            if (AbpSession.TenantId != null)
            {
                item.TenantId = (int?)AbpSession.TenantId;
            }
            if (AbpSession.UserId != null)
            {
                item.created_by_id = AbpSession.UserId;
            }
            item.created_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            await _TrungTamsRepository.InsertAsync(item);
        }

        /// <summary>
        /// Update TrungTam
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Update(CreateOrEditTrungTamDto input)
        {
            var item = await _TrungTamsRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == (int)input.Id);
            if (AbpSession.UserId != null)
            {
                item.updated_by_id = AbpSession.UserId;
            }
            item.updated_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            ObjectMapper.Map(input, item);
        }

        /// <summary>
        /// Delete TrungTam
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/trung-tam")]
        // [HttpPost]
        public async Task Delete(EntityDto input)
        {
            await _TrungTamsRepository.DeleteAsync(input.Id);
        }

        /// <summary>
        /// Get all TrungTam for table dropdown
        /// </summary>
        /// <returns></returns>
        [Route("api/services/app/trung-tam/list")]
        public async Task<List<TrungTamOutput>> GetAllForTableDropdown()
        {
            if (AbpSession.UserId == null)
            {
                throw new UserFriendlyException("There is no current user!");
            }
            var currentUser = await _userManager.GetUserByIdAsync(AbpSession.GetUserId());
            var roles = await _userManager.GetRolesAsync(currentUser);
            var isManager = roles.Contains("Lanh_Dao");
            var scope = currentUser.Surname.ToLower();

            return await _TrungTamsRepository.GetAll()
                .Where(e => e.published_at.HasValue)
                .WhereIf(!isManager, e => scope.Contains(e.ma.ToLower()))
                .OrderBy(e => e.thu_tu)
                .Select(item => new TrungTamOutput
                {
                    id = item.Id,
                    ma = item.ma,
                    ten = item.ten,
                    dan_so = item.dan_so,
                    ho_dan = item.ho_dan
                }).ToListAsync();
        }
    }
}