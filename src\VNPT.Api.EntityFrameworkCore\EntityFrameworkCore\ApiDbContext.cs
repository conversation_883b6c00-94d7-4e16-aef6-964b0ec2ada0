﻿using Abp.Localization;
using Abp.Zero.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;
using VNPT.Api.Authorization.Roles;
using VNPT.Api.Authorization.Users;
using VNPT.Api.Entities;
using VNPT.Api.MultiTenancy;

namespace VNPT.Api.EntityFrameworkCore
{
    public class ApiDbContext : AbpZeroDbContext<Tenant, Role, User, ApiDbContext>
    {
        /* Define a DbSet for each entity of the application */
        public virtual DbSet<CauHinh> CauHinh { get; set; }
        public virtual DbSet<DichVu> DichVu { get; set; }
        public virtual DbSet<NhanVien> NhanVien { get; set; }
        public virtual DbSet<ThueBao> ThueBao { get; set; }
        public virtual DbSet<TrangThai> TrangThai { get; set; }
        public virtual DbSet<TrungTam> TrungTam { get; set; }
        public virtual DbSet<ToKyThuat> ToKyThuat { get; set; }
        public virtual DbSet<DiaBan> DiaBan { get; set; }
        public virtual DbSet<PhongBanHang> PhongBanHang { get; set; }

        public virtual DbSet<Tinh> Tinh { get; set; }
        public virtual DbSet<Huyen> Huyen { get; set; }
        public virtual DbSet<Xa> Xa { get; set; }
        public virtual DbSet<ApKhuPho> ApKhuPho { get; set; }

        public virtual DbSet<TapTin> TapTin { get; set; }
        public virtual DbSet<GhiChu> GhiChu { get; set; }
        public ApiDbContext(DbContextOptions<ApiDbContext> options) : base(options)
        {
            //PostgreSql Configure DateTime Handling 
            AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);
            AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<ApplicationLanguageText>()
            .Property(p => p.Value)
            .HasMaxLength(100); // any integer that is smaller than 10485760
        }
    }
}
