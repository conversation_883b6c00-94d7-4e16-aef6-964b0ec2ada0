﻿using Abp.Domain.Entities;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace VNPT.Api.Entities
{
    [Table("thue_bao", Schema = "vnpt")]
    public class ThueBao : Entity, IMayHaveTenant
    {
        [Column("id")]
        public override int Id { get; set; }
        [Column("tenant_id")]
        public int? TenantId { get; set; }
        public DateTime? created_at { get; set; }
        public DateTime? updated_at { get; set; }
        public DateTime? published_at { get; set; }
        public long? created_by_id { get; set; }
        public long? updated_by_id { get; set; }
        public string trung_tam { get; set; }
        public string to_ky_thuat { get; set; }
        public string dia_ban { get; set; }
        public string nhan_vien_ky_thuat { get; set; }
        public string phong_ban_hang { get; set; }
        public string nhan_vien_kinh_doanh { get; set; }
        public string ma_thue_bao { get; set; }
        public string ma_thanh_toan { get; set; }
        public string ten_thue_bao { get; set; }
        public int? tuoi_thue_bao { get; set; }
        public int? dich_vu_id { get; set; }
        public long? doanh_thu_phat_sinh { get; set; }
        public string thiet_bi_dau_cuoi { get; set; }
        public string serial_number { get; set; }
        public string toc_do { get; set; }
        public int? toc_do_thuc_te { get; set; }
        public DateTime? ngay_su_dung { get; set; }
        public int? trang_thai_id { get; set; }
        public float? kinh_do { get; set; }
        public float? vi_do { get; set; }
        public string dia_chi_thanh_toan { get; set; }
        public string dia_chi_lap_dat { get; set; }
        public int? tinh_id { get; set; }
        public int? huyen_id { get; set; }
        public int? xa_id { get; set; }
        public string pho { get; set; }
        public string ap { get; set; }
        public string khu_pho { get; set; }
        public string so_nha { get; set; }
        public string ket_cuoi { get; set; }
        public string vi_tri { get; set; }

        [ForeignKey("dich_vu_id")]
        public DichVu DichVuFk { get; set; }

        [ForeignKey("trang_thai_id")]
        public TrangThai TrangThaiFk { get; set; }

        [ForeignKey("tinh_id")]
        public Tinh TinhFk { get; set; }

        [ForeignKey("huyen_id")]
        public Huyen HuyenFk { get; set; }

        [ForeignKey("xa_id")]
        public Xa XaFk { get; set; }
    }
}