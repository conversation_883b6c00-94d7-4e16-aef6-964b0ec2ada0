﻿using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Authorization;
using VNPT.Api.Entities;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for Tinh
    /// </summary>
    [AbpAuthorize(PermissionNames.Pages_Tinh)]
    public class TinhAppService : ApiAppServiceBase
    {
        private readonly IRepository<Tinh> _TinhsRepository;

        /// <summary>
        /// Service for Tinh
        /// </summary>
        public TinhAppService(IRepository<Tinh> TinhsRepository)
        {
            _TinhsRepository = TinhsRepository;
        }

        /// <summary>
        /// Get all Tinh
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/tinh")]
        public async Task<PagedResultDto<GetTinhOutput>> GetAll(GetAllTinhsInput input)
        {
            var filtered = _TinhsRepository.GetAll()
                        .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false || e.ma.ToLower().Contains(input.keyword.ToLower()) || e.ten.ToLower().Contains(input.keyword.ToLower()))
                        .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue);

            input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
            var pagedAndFiltered = filtered
                .OrderBy(input.Sorting ?? "id asc")
                .PageBy(input);

            var totalCount = await filtered.CountAsync();
            var results = new List<GetTinhOutput>();
            foreach (var item in pagedAndFiltered)
            {
                var output = ObjectMapper.Map<GetTinhOutput>(item);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return new PagedResultDto<GetTinhOutput>(
                totalCount,
                results
            );

        }

        /// <summary>
        /// Get Tinh by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/services/app/tinh/{id}")]
        public async Task<GetTinhOutput> Get(int id)
        {
            var item = await _TinhsRepository.FirstOrDefaultAsync(e => e.Id == id);
            if (item == null)
            {
                throw new UserFriendlyException("id does not exist");
            }
            var output = ObjectMapper.Map<GetTinhOutput>(item);
            output.tinh_trang = item.published_at.HasValue;
            return output;
        }

        /// <summary>
        /// Create or Edit Tinh
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/tinh")]
        public async Task CreateOrEdit(CreateOrEditTinhDto input)
        {
            if (input.Id == null)
            {
                await Create(input);
            }
            else
            {
                await Update(input);
            }
        }

        /// <summary>
        /// Create Tinh
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Create(CreateOrEditTinhDto input)
        {
            var item = ObjectMapper.Map<Tinh>(input);
            if (AbpSession.TenantId != null)
            {
                item.TenantId = (int?)AbpSession.TenantId;
            }
            if (AbpSession.UserId != null)
            {
                item.created_by_id = AbpSession.UserId;
            }
            item.created_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            await _TinhsRepository.InsertAsync(item);
        }

        /// <summary>
        /// Update Tinh
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Update(CreateOrEditTinhDto input)
        {
            var item = await _TinhsRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == (int)input.Id);
            if (AbpSession.UserId != null)
            {
                item.updated_by_id = AbpSession.UserId;
            }
            item.updated_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            ObjectMapper.Map(input, item);
        }

        /// <summary>
        /// Delete Tinh
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/tinh")]
        // [HttpPost]
        public async Task Delete(EntityDto input)
        {
            await _TinhsRepository.DeleteAsync(input.Id);
        }

        /// <summary>
        /// Get all Tinh for table dropdown
        /// </summary>
        /// <returns></returns>
        [Route("api/services/app/tinh/list")]
        public async Task<List<TinhOutput>> GetAllForTableDropdown()
        {
            return await _TinhsRepository.GetAll()
                .Where(e => e.published_at.HasValue)
                .OrderBy(e => e.thu_tu)
                .Select(item => new TinhOutput
                {
                    id = item.Id,
                    ten = item.ten
                }).ToListAsync();
        }
    }
}