﻿using System;
using System.Threading.Tasks;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for DongBo
    /// </summary>
    public class DongBoAppService : ApiAppServiceBase
    {
        /// <summary>
        /// Service for DongBo
        /// </summary>
        public DongBoAppService()
        {
        }

        /// <summary>
        /// Task for data synchronization
        /// </summary>
        /// <returns>Task for async completion</returns>
        [Route("api/services/app/dong-bo/data")]
        public async Task doDongBoData()
        {
            // Record task execution in log
            Logger.Info("Starting data synchronization job");
            
            try
            {
                // Perform synchronization work
                Console.WriteLine("Đã đồng bộ thành công");
                
                // Log successful completion
                Logger.Info("Data synchronization completed successfully");
            }
            catch (Exception ex)
            {
                // Log any exceptions
                Logger.Error("Error in data synchronization job", ex);
                throw new UserFriendlyException("Error in data synchronization job");
            }
            
            await Task.CompletedTask;
        }
    }
}