using Abp.Authorization;
using Abp.Localization;
using Abp.MultiTenancy;

namespace VNPT.Api.Authorization
{
    public class ApiAuthorizationProvider : AuthorizationProvider
    {
        public override void SetPermissions(IPermissionDefinitionContext context)
        {
            context.CreatePermission(PermissionNames.Pages_Users, L("Users"));
            context.CreatePermission(PermissionNames.Pages_Roles, L("Roles"));
            context.CreatePermission(PermissionNames.Pages_Tenants, L("Tenants"), multiTenancySides: MultiTenancySides.Host);
            context.CreatePermission(PermissionNames.Pages_Administration, L("Administration"));
            context.CreatePermission(PermissionNames.Pages_Catalog, L("Catalog"));
            context.CreatePermission(PermissionNames.Pages_Config, L("Config"));

            context.CreatePermission(PermissionNames.Pages_TrangChu, L("TrangChu"));
            context.CreatePermission(PermissionNames.Pages_<PERSON>ch<PERSON><PERSON>, L("DichVu"));
            context.CreatePermission(PermissionNames.Pages_Ghi<PERSON>hu, L("GhiChu"));
            context.CreatePermission(PermissionNames.Pages_NhanVien, L("NhanVien"));
            context.CreatePermission(PermissionNames.Pages_ThueBao, L("ThueBao"));
            context.CreatePermission(PermissionNames.Command_ThueBao_Import, L("ThueBao.Import"));
            context.CreatePermission(PermissionNames.Pages_TrangThai, L("TrangThai"));
            context.CreatePermission(PermissionNames.Pages_TrungTam, L("TrungTam"));
            context.CreatePermission(PermissionNames.Pages_ToKyThuat, L("ToKyThuat"));
            context.CreatePermission(PermissionNames.Pages_DiaBan, L("DiaBan")); ;
            context.CreatePermission(PermissionNames.Pages_PhongBanHang, L("PhongBanHang"));

            context.CreatePermission(PermissionNames.Pages_Tinh, L("Tinh"));
            context.CreatePermission(PermissionNames.Pages_Huyen, L("Huyen"));
            context.CreatePermission(PermissionNames.Pages_Xa, L("Xa"));
            context.CreatePermission(PermissionNames.Pages_ApKhuPho, L("ApKhuPho"));
        }

        private static ILocalizableString L(string name)
        {
            return new LocalizableString(name, ApiConsts.LocalizationSourceName);
        }
    }
}
