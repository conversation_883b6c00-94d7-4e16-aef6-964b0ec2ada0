using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Authorization;
using VNPT.Api.Entities;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for ApKhuPho
    /// </summary>
    [AbpAuthorize(PermissionNames.Pages_ApKhuPho)]
    public class ApKhuPhoAppService : ApiAppServiceBase
    {
        private readonly IRepository<ApKhuPho> _ApKhuPhosRepository;
        private readonly IRepository<ThueBao> _ThueBaosRepository;

        /// <summary>
        /// Service for Ap
        /// </summary>
        public ApKhuPhoAppService(IRepository<ApKhuPho> ApKhuPhosRepository, IRepository<ThueBao> ThueBaosRepository)
        {
            _ApKhuPhosRepository = ApKhuPhosRepository;
            _ThueBaosRepository = ThueBaosRepository;
        }

        /// <summary>
        /// Get all ApKhuPho
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/ap-khu-pho")]
        public async Task<PagedResultDto<GetApKhuPhoOutput>> GetAll(GetAllApKhuPhosInput input)
        {
            var filtered = _ApKhuPhosRepository.GetAll()
                        .Include(e => e.XaFk)
                        .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false || e.ma.ToLower().Contains(input.keyword.ToLower()) || e.ten.ToLower().Contains(input.keyword.ToLower()))
                        .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue);

            input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
            var pagedAndFiltered = filtered
                .OrderBy(input.Sorting ?? "id asc")
                .PageBy(input);

            var totalCount = await filtered.CountAsync();
            var results = new List<GetApKhuPhoOutput>();
            foreach (var item in pagedAndFiltered)
            {
                var output = ObjectMapper.Map<GetApKhuPhoOutput>(item);
                output.xa = ObjectMapper.Map<XaOutput>(item.XaFk);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return new PagedResultDto<GetApKhuPhoOutput>(
                totalCount,
                results
            );

        }

        /// <summary>
        /// Get ApKhuPho by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/services/app/ap-khu-pho/{id}")]
        public async Task<GetApKhuPhoOutput> Get(int id)
        {
            var item = await _ApKhuPhosRepository
                    .GetAll()
                    .Include(e => e.XaFk)
                    .FirstOrDefaultAsync(e => e.Id == id);
            if (item == null)
            {
                throw new UserFriendlyException("id does not exist");
            }
            var output = ObjectMapper.Map<GetApKhuPhoOutput>(item);
            output.xa = ObjectMapper.Map<XaOutput>(item.XaFk);
            output.tinh_trang = item.published_at.HasValue;
            return output;
        }

        /// <summary>
        /// Create or Edit ApKhuPho
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/ap-khu-pho")]
        public async Task CreateOrEdit(CreateOrEditApKhuPhoDto input)
        {
            if (input.Id == null)
            {
                await Create(input);
            }
            else
            {
                await Update(input);
            }
        }

        /// <summary>
        /// Create ApKhuPho
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Create(CreateOrEditApKhuPhoDto input)
        {
            var item = ObjectMapper.Map<ApKhuPho>(input);
            if (AbpSession.TenantId != null)
            {
                item.TenantId = (int?)AbpSession.TenantId;
            }
            if (AbpSession.UserId != null)
            {
                item.created_by_id = AbpSession.UserId;
            }
            item.created_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            await _ApKhuPhosRepository.InsertAsync(item);
        }

        /// <summary>
        /// Update ApKhuPho
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Update(CreateOrEditApKhuPhoDto input)
        {
            var item = await _ApKhuPhosRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == (int)input.Id);
            if (AbpSession.UserId != null)
            {
                item.updated_by_id = AbpSession.UserId;
            }
            item.updated_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            ObjectMapper.Map(input, item);
        }

        /// <summary>
        /// Delete ApKhuPho
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/ap-khu-pho")]
        // [HttpPost]
        public async Task Delete(EntityDto input)
        {
            await _ApKhuPhosRepository.DeleteAsync(input.Id);
        }

        /// <summary>
        /// Get all ApKhuPho for table dropdown
        /// </summary>
        /// <returns></returns>
        [Route("api/services/app/ap-khu-pho/list")]
        public async Task<List<ApKhuPhoOutput>> GetAllForTableDropdown(GetAllApKhuPhosInput input)
        {
            var listAp = await _ThueBaosRepository.GetAll()
                        .Where(e => !string.IsNullOrEmpty(e.ap))
                        .WhereIf(input.xa_id.HasValue, e => input.xa_id == e.xa_id)
                        .GroupBy(e => e.ap)
                        .Select(item => new ApKhuPhoOutput
                        {
                            // id = item.Id,
                            ten = item.Key,
                            xa_id = input.xa_id
                        }).ToListAsync();
            var listKhuPho = await _ThueBaosRepository.GetAll()
                        .Where(e => !string.IsNullOrEmpty(e.khu_pho))
                        .WhereIf(input.xa_id.HasValue, e => input.xa_id == e.xa_id)
                        .GroupBy(e => e.khu_pho)
                        .Select(item => new ApKhuPhoOutput
                        {
                            // id = item.Id,
                            ten = item.Key,
                            xa_id = input.xa_id
                        }).ToListAsync();
            return listAp.Union(listKhuPho).OrderBy(e => e.ten).ToList();
        }
    }
}
