using System.Threading.Tasks;
using VNPT.Api.Authorization.Accounts.Dto;
using VNPT.Api.Authorization.Users;

namespace VNPT.Api.Authorization.Accounts
{
    public class AccountAppService : ApiAppServiceBase, IAccountAppService
    {
        // from: http://regexlib.com/REDetails.aspx?regexp_id=1923
        public const string PasswordRegex = "(?=^.{8,}$)(?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?!.*\\s)[0-9a-zA-Z!@#$%^&*()]*$";

        private readonly UserRegistrationManager _userRegistrationManager;

        public AccountAppService(
            UserRegistrationManager userRegistrationManager)
        {
            _userRegistrationManager = userRegistrationManager;
        }

        public async Task<IsTenantAvailableOutput> IsTenantAvailable(IsTenantAvailableInput input)
        {
            var tenant = await TenantManager.FindByTenancyNameAsync(input.TenancyName);
            if (tenant == null)
            {
                return new IsTenantAvailableOutput(TenantAvailabilityState.NotFound);
            }

            if (!tenant.IsActive)
            {
                return new IsTenantAvailableOutput(TenantAvailabilityState.InActive);
            }

            return new IsTenantAvailableOutput(TenantAvailabilityState.Available, tenant.Id);
        }

        //public async Task<RegisterOutput> Register(RegisterInput input)
        //{
        //    var user = await _userRegistrationManager.RegisterAsync(
        //        input.Name,
        //        input.Surname,
        //        input.EmailAddress,
        //        input.UserName,
        //        input.Password,
        //        true // Assumed email address is always confirmed. Change this if you want to implement email confirmation.
        //    );

        //    var isEmailConfirmationRequiredForLogin = await SettingManager.GetSettingValueAsync<bool>(AbpZeroSettingNames.UserManagement.IsEmailConfirmationRequiredForLogin);

        //    return new RegisterOutput
        //    {
        //        CanLogin = user.IsActive && (user.IsEmailConfirmed || !isEmailConfirmationRequiredForLogin)
        //    };
        //}
    }
}
