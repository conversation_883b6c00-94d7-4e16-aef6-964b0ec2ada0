using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Authorization;
using VNPT.Api.Entities;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for GhiChu
    /// </summary>
    [AbpAuthorize(PermissionNames.Pages_GhiChu)]
    public class GhiChuAppService : ApiAppServiceBase
    {
        private readonly IRepository<GhiChu> _ghiChusRepository;

        /// <summary>
        /// Service for GhiChu
        /// </summary>
        public GhiChuAppService(IRepository<GhiChu> ghiChusRepository)
        {
            _ghiChusRepository = ghiChusRepository;
        }

        /// <summary>
        /// Get all GhiChu
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/ghi-chu")]
        public async Task<PagedResultDto<GetGhiChuOutput>> GetAll(GetAllGhiChusInput input)
        {
            var filtered = _ghiChusRepository.GetAll()
                        .Include(e => e.ThueBaoFk)
                        .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false 
                            || e.noi_dung.ToLower().Contains(input.keyword.ToLower())
                            || e.ThueBaoFk.ma_thue_bao.ToLower().Contains(input.keyword.ToLower())
                            || e.ThueBaoFk.ten_thue_bao.ToLower().Contains(input.keyword.ToLower()))
                        .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue)
                        .WhereIf(input.thue_bao_id.HasValue, e => e.thue_bao_id == input.thue_bao_id);

            input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
            var pagedAndFiltered = filtered
                .OrderBy(input.Sorting ?? "id desc")
                .PageBy(input);

            var totalCount = await filtered.CountAsync();
            var results = new List<GetGhiChuOutput>();
            foreach (var item in pagedAndFiltered)
            {
                var output = ObjectMapper.Map<GetGhiChuOutput>(item);
                output.thue_bao = ObjectMapper.Map<ThueBaoOutput>(item.ThueBaoFk);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return new PagedResultDto<GetGhiChuOutput>(
                totalCount,
                results
            );
        }

        /// <summary>
        /// Get GhiChu by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/services/app/ghi-chu/{id}")]
        public async Task<GetGhiChuOutput> Get(int id)
        {
            var item = await _ghiChusRepository
                    .GetAll()
                    .Include(e => e.ThueBaoFk)
                    .FirstOrDefaultAsync(e => e.Id == id);
            if (item == null)
            {
                throw new UserFriendlyException("id does not exist");
            }
            var output = ObjectMapper.Map<GetGhiChuOutput>(item);
            output.thue_bao = ObjectMapper.Map<ThueBaoOutput>(item.ThueBaoFk);
            output.tinh_trang = item.published_at.HasValue;
            return output;
        }

        /// <summary>
        /// Create or Edit GhiChu
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/ghi-chu")]
        public async Task CreateOrEdit(CreateOrEditGhiChuDto input)
        {
            if (input.Id == null)
            {
                await Create(input);
            }
            else
            {
                await Update(input);
            }
        }

        /// <summary>
        /// Create GhiChu
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Create(CreateOrEditGhiChuDto input)
        {
            var item = ObjectMapper.Map<GhiChu>(input);
            if (AbpSession.TenantId != null)
            {
                item.TenantId = (int?)AbpSession.TenantId;
            }
            if (AbpSession.UserId != null)
            {
                item.created_by_id = AbpSession.UserId;
            }
            item.created_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            await _ghiChusRepository.InsertAsync(item);
        }

        /// <summary>
        /// Update GhiChu
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Update(CreateOrEditGhiChuDto input)
        {
            var item = await _ghiChusRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == (int)input.Id);
            if (AbpSession.UserId != null)
            {
                item.updated_by_id = AbpSession.UserId;
            }
            item.updated_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            ObjectMapper.Map(input, item);
        }

        /// <summary>
        /// Delete GhiChu
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/ghi-chu")]
        public async Task Delete(EntityDto input)
        {
            await _ghiChusRepository.DeleteAsync(input.Id);
        }

        /// <summary>
        /// Get GhiChu by ThueBao id
        /// </summary>
        /// <param name="thue_bao_id"></param>
        /// <returns></returns>
        [Route("api/services/app/ghi-chu/by-thue-bao/{thue_bao_id}")]
        public async Task<List<GetGhiChuOutput>> GetByThueBaoId(int thue_bao_id)
        {
            var items = await _ghiChusRepository.GetAll()
                .Include(e => e.ThueBaoFk)
                .Where(e => e.thue_bao_id == thue_bao_id && e.published_at.HasValue)
                .OrderByDescending(e => e.ngay_thang)
                .ToListAsync();

            var results = new List<GetGhiChuOutput>();
            foreach (var item in items)
            {
                var output = ObjectMapper.Map<GetGhiChuOutput>(item);
                output.thue_bao = ObjectMapper.Map<ThueBaoOutput>(item.ThueBaoFk);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return results;
        }
    }
}
