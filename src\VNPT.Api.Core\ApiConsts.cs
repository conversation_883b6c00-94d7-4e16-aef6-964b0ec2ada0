﻿using VNPT.Api.Debugging;

namespace VNPT.Api
{
    public class ApiConsts
    {
        public const string LocalizationSourceName = "Api";

        public const string ConnectionStringName = "Default";

        public const bool MultiTenancyEnabled = true;


        /// <summary>
        /// Default pass phrase for SimpleStringCipher decrypt/encrypt operations
        /// </summary>
        public static readonly string DefaultPassPhrase =
            DebugHelper.IsDebug ? "gsKxGZ012HLL3MI5" : "b5dfa0e3a3ca4eeda1725c25459c255e";
    }
}
