﻿using Abp.Authorization;
using Abp.Runtime.Session;
using System.Threading.Tasks;
using VNPT.Api.Configuration.Dto;

namespace VNPT.Api.Configuration
{
    [AbpAuthorize]
    public class ConfigurationAppService : ApiAppServiceBase, IConfigurationAppService
    {
        public async Task ChangeUiTheme(ChangeUiThemeInput input)
        {
            await SettingManager.ChangeSettingForUserAsync(AbpSession.ToUserIdentifier(), AppSettingNames.UiTheme, input.Theme);
        }
    }
}
