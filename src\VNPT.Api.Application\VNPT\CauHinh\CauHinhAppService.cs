﻿using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Authorization;
using VNPT.Api.Entities;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for CauHinh
    /// </summary>
    [AbpAuthorize(PermissionNames.Pages_Config)]
    public class CauHinhAppService : ApiAppServiceBase
    {
        private readonly IRepository<CauHinh> _CauHinhsRepository;

        /// <summary>
        /// Service for CauHinh
        /// </summary>
        public CauHinhAppService(IRepository<CauHinh> CauHinhsRepository)
        {
            _CauHinhsRepository = CauHinhsRepository;
        }

        /// <summary>
        /// Get all CauHinh
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/cau-hinh")]
        public async Task<PagedResultDto<GetCauHinhOutput>> GetAll(GetAllCauHinhsInput input)
        {
            var filtered = _CauHinhsRepository.GetAll()
                        .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false || e.ma.ToLower().Contains(input.keyword.ToLower()) || e.ten.ToLower().Contains(input.keyword.ToLower()) || e.gia_tri.ToLower().Contains(input.keyword.ToLower()))
                        .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue);

            input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
            var pagedAndFiltered = filtered
                .OrderBy(input.Sorting ?? "id asc")
                .PageBy(input);

            var totalCount = await filtered.CountAsync();
            var results = new List<GetCauHinhOutput>();
            foreach (var item in pagedAndFiltered)
            {
                var output = ObjectMapper.Map<GetCauHinhOutput>(item);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return new PagedResultDto<GetCauHinhOutput>(
                totalCount,
                results
            );
        }

        /// <summary>
        /// Get CauHinh by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/services/app/cau-hinh/{id}")]
        public async Task<GetCauHinhOutput> Get(int id)
        {
            var item = await _CauHinhsRepository
                    .GetAll()
                    .FirstOrDefaultAsync(e => e.Id == id);
            if (item == null)
            {
                throw new UserFriendlyException("id does not exist");
            }
            var output = ObjectMapper.Map<GetCauHinhOutput>(item);
            output.tinh_trang = item.published_at.HasValue;
            return output;
        }

        /// <summary>
        /// Create or Edit CauHinh
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/cau-hinh")]
        public async Task CreateOrEdit(CreateOrEditCauHinhDto input)
        {
            if (input.Id == null)
            {
                await Create(input);
            }
            else
            {
                await Update(input);
            }
        }

        /// <summary>
        /// Create CauHinh
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Create(CreateOrEditCauHinhDto input)
        {
            var item = ObjectMapper.Map<CauHinh>(input);
            if (AbpSession.TenantId != null)
            {
                item.TenantId = (int?)AbpSession.TenantId;
            }
            if (AbpSession.UserId != null)
            {
                item.created_by_id = AbpSession.UserId;
            }
            item.created_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            await _CauHinhsRepository.InsertAsync(item);
        }

        /// <summary>
        /// Update CauHinh
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Update(CreateOrEditCauHinhDto input)
        {
            var item = await _CauHinhsRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == (int)input.Id);
            if (AbpSession.UserId != null)
            {
                item.updated_by_id = AbpSession.UserId;
            }
            item.updated_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            ObjectMapper.Map(input, item);
        }

        /// <summary>
        /// Delete CauHinh
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/cau-hinh")]
        // [HttpPost]
        public async Task Delete(EntityDto input)
        {
            await _CauHinhsRepository.DeleteAsync(input.Id);
        }
    }
}