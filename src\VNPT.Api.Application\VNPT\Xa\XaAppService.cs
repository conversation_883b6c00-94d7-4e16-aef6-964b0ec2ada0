﻿using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Authorization;
using VNPT.Api.Entities;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for Xa
    /// </summary>
    [AbpAuthorize(PermissionNames.Pages_Xa)]
    public class XaAppService : ApiAppServiceBase
    {
        private readonly IRepository<Xa> _XasRepository;

        /// <summary>
        /// Service for Xa
        /// </summary>
        public XaAppService(IRepository<Xa> XasRepository)
        {
            _XasRepository = XasRepository;
        }

        /// <summary>
        /// Get all Xa
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/xa")]
        public async Task<PagedResultDto<GetXaOutput>> GetAll(GetAllXasInput input)
        {
            var filtered = _XasRepository.GetAll()
                        .Include(e => e.HuyenFk)
                        .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false || e.ma.ToLower().Contains(input.keyword.ToLower()) || e.ten.ToLower().Contains(input.keyword.ToLower()))
                        .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue);

            input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
            var pagedAndFiltered = filtered
                .OrderBy(input.Sorting ?? "id asc")
                .PageBy(input);

            var totalCount = await filtered.CountAsync();
            var results = new List<GetXaOutput>();
            foreach (var item in pagedAndFiltered)
            {
                var output = ObjectMapper.Map<GetXaOutput>(item);
                output.huyen = ObjectMapper.Map<HuyenOutput>(item.HuyenFk);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return new PagedResultDto<GetXaOutput>(
                totalCount,
                results
            );

        }

        /// <summary>
        /// Get Xa by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/services/app/xa/{id}")]
        public async Task<GetXaOutput> Get(int id)
        {
            var item = await _XasRepository
                    .GetAll()
                    .Include(e => e.HuyenFk)
                    .FirstOrDefaultAsync(e => e.Id == id);
            if (item == null)
            {
                throw new UserFriendlyException("id does not exist");
            }
            var output = ObjectMapper.Map<GetXaOutput>(item);
            output.huyen = ObjectMapper.Map<HuyenOutput>(item.HuyenFk);
            output.tinh_trang = item.published_at.HasValue;
            return output;
        }

        /// <summary>
        /// Create or Edit Xa
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/xa")]
        public async Task CreateOrEdit(CreateOrEditXaDto input)
        {
            if (input.Id == null)
            {
                await Create(input);
            }
            else
            {
                await Update(input);
            }
        }

        /// <summary>
        /// Create Xa
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Create(CreateOrEditXaDto input)
        {
            var item = ObjectMapper.Map<Xa>(input);
            if (AbpSession.TenantId != null)
            {
                item.TenantId = (int?)AbpSession.TenantId;
            }
            if (AbpSession.UserId != null)
            {
                item.created_by_id = AbpSession.UserId;
            }
            item.created_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            await _XasRepository.InsertAsync(item);
        }

        /// <summary>
        /// Update Xa
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Update(CreateOrEditXaDto input)
        {
            var item = await _XasRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == (int)input.Id);
            if (AbpSession.UserId != null)
            {
                item.updated_by_id = AbpSession.UserId;
            }
            item.updated_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            ObjectMapper.Map(input, item);
        }

        /// <summary>
        /// Delete Xa
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/xa")]
        // [HttpPost]
        public async Task Delete(EntityDto input)
        {
            await _XasRepository.DeleteAsync(input.Id);
        }

        /// <summary>
        /// Get all Xa for table dropdown
        /// </summary>
        /// <returns></returns>
        [Route("api/services/app/xa/list")]
        public async Task<List<XaOutput>> GetAllForTableDropdown()
        {
            return await _XasRepository.GetAll()
                .Where(e => e.published_at.HasValue)
                .OrderBy(e => e.ten)
                .Select(item => new XaOutput
                {
                    id = item.Id,
                    ten = item.ten,
                    huyen_id = item.huyen_id,
                }).ToListAsync();
        }
    }
}