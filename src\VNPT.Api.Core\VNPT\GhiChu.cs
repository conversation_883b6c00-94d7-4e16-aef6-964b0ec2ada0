using Abp.Domain.Entities;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace VNPT.Api.Entities
{
    [Table("ghi_chu", Schema = "vnpt")]
    public class GhiChu : Entity, IMayHaveTenant
    {
        [Column("id")]
        public override int Id { get; set; }
        [Column("tenant_id")]
        public int? TenantId { get; set; }
        public DateTime? created_at { get; set; }
        public DateTime? updated_at { get; set; }
        public DateTime? published_at { get; set; }
        public long? created_by_id { get; set; }
        public long? updated_by_id { get; set; }
        public int thue_bao_id { get; set; }
        public string noi_dung { get; set; }
        public DateTime? ngay_thang { get; set; }

        [ForeignKey("thue_bao_id")]
        public ThueBao ThueBaoFk { get; set; }
    }
}
