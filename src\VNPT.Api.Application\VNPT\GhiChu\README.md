# Module Ghi Chú - VNPT Customer Management

## <PERSON><PERSON> tả
Module Ghi Chú cho phép quản lý các ghi chú liên quan đến thuê bao trong hệ thống quản lý khách hàng VNPT.

## Cấu trúc module

### 1. Entity (\src\VNPT.Api.Core\VNPT\GhiChu.cs)
- **thue_bao_id**: ID thuê bao (bắt buộc)
- **noi_dung**: Nội dung ghi chú
- **ngay_thang**: <PERSON><PERSON><PERSON> tháng ghi chú
- Kế thừa các trường chuẩn từ base entity (id, tenant_id, created_at, updated_at, published_at, created_by_id, updated_by_id)

### 2. DTOs (\src\VNPT.Api.Application\VNPT\GhiChu\Dtos\)
- **GhiChuDto.cs**: DTO cơ bản
- **CreateOrEditGhiChuDto.cs**: DTO cho tạo/sửa
- **GetAllGhiChusInput.cs**: DTO cho tham số tìm kiếm
- **GetGhiChuOutput.cs**: DTO cho kết quả trả về
- **ThueBaoFk.cs**: DTO cho thông tin thuê bao
- **GhiChuMapProfile.cs**: Cấu hình AutoMapper

### 3. Application Services
- **GhiChuAppService.cs**: Implementation service

## Các API Endpoints

### Danh sách ghi chú
```
GET /api/services/app/ghi-chu
Parameters: keyword, status, thue_bao_id, sorting, skipCount, maxResultCount
```

### Chi tiết ghi chú
```
GET /api/services/app/ghi-chu/{id}
```

### Tạo/Cập nhật ghi chú
```
POST /api/services/app/ghi-chu
Body: CreateOrEditGhiChuDto
```

### Xóa ghi chú
```
DELETE /api/services/app/ghi-chu
Body: { id: number }
```

### Danh sách ghi chú cho dropdown
```
GET /api/services/app/ghi-chu/list
```

### Ghi chú theo thuê bao
```
GET /api/services/app/ghi-chu/by-thue-bao/{thueBaoId}
```

## Cách sử dụng

### 1. Chạy SQL Script để tạo bảng
```sql
-- Chạy file create_ghi_chus_table.sql trong PostgreSQL
```

### 2. Thiết lập quyền
Module sử dụng permission `Pages.GhiChu` đã được cấu hình trong:
- `PermissionNames.cs`
- `ApiAuthorizationProvider.cs`

### 3. Cấu hình AutoMapper
AutoMapper đã được cấu hình trong `CustomDtoMapper.cs`

### 4. Dependency Injection
Service đã được đăng ký tự động thông qua ABP Framework

## Ví dụ sử dụng API

### Tạo ghi chú mới
```json
POST /api/services/app/ghi-chu
{
    "thue_bao_id": 1,
    "noi_dung": "Khách hàng yêu cầu nâng cấp tốc độ",
    "ngay_thang": "2024-01-15T10:30:00",
    "tinh_trang": true
}
```

### Tìm kiếm ghi chú
```
GET /api/services/app/ghi-chu?keyword=nâng cấp&thue_bao_id=1&status=true&maxResultCount=10
```

### Lấy ghi chú theo thuê bao
```
GET /api/services/app/ghi-chu/by-thue-bao/1
```

## Tính năng chính

1. **Quản lý CRUD**: Tạo, đọc, cập nhật, xóa ghi chú
2. **Tìm kiếm nâng cao**: Theo keyword, thuê bao, trạng thái
3. **Phân trang**: Hỗ trợ phân trang cho danh sách
4. **Quan hệ với thuê bao**: Mỗi ghi chú liên kết với một thuê bao
5. **Audit trail**: Theo dõi người tạo, cập nhật và thời gian
6. **Multi-tenant**: Hỗ trợ đa tenant

## Lưu ý kỹ thuật

1. **Foreign Key**: Quan hệ với bảng `thue_baos` qua `thue_bao_id`
2. **Index**: Đã tạo index cho hiệu suất tốt hơn
3. **Validation**: Cần thêm validation tùy theo yêu cầu business
4. **Authorization**: Sử dụng `[AbpAuthorize(PermissionNames.Pages_GhiChu)]`
5. **Soft Delete**: Sử dụng `published_at` để đánh dấu trạng thái
