﻿using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Authorization;
using VNPT.Api.Entities;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for TrangThai
    /// </summary>
    [AbpAuthorize(PermissionNames.Pages_TrangThai)]
    public class TrangThaiAppService : ApiAppServiceBase
    {
        private readonly IRepository<TrangThai> _TrangThaisRepository;

        /// <summary>
        /// Service for TrangThai
        /// </summary>
        public TrangThaiAppService(IRepository<TrangThai> TrangThaisRepository)
        {
            _TrangThaisRepository = TrangThaisRepository;
        }

        /// <summary>
        /// Get all TrangThai
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/trang-thai")]
        public async Task<PagedResultDto<GetTrangThaiOutput>> GetAll(GetAllTrangThaisInput input)
        {
            var filtered = _TrangThaisRepository.GetAll()
                        .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false || e.ma.ToLower().Contains(input.keyword.ToLower()) || e.ten.ToLower().Contains(input.keyword.ToLower()))
                        .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue);

            input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
            var pagedAndFiltered = filtered
                .OrderBy(input.Sorting ?? "id asc")
                .PageBy(input);

            var totalCount = await filtered.CountAsync();
            var results = new List<GetTrangThaiOutput>();
            foreach (var item in pagedAndFiltered)
            {
                var output = ObjectMapper.Map<GetTrangThaiOutput>(item);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return new PagedResultDto<GetTrangThaiOutput>(
                totalCount,
                results
            );

        }

        /// <summary>
        /// Get TrangThai by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/services/app/trang-thai/{id}")]
        public async Task<GetTrangThaiOutput> Get(int id)
        {
            var item = await _TrangThaisRepository.FirstOrDefaultAsync(e => e.Id == id);
            if (item == null)
            {
                throw new UserFriendlyException("id does not exist");
            }
            var output = ObjectMapper.Map<GetTrangThaiOutput>(item);
            output.tinh_trang = item.published_at.HasValue;
            return output;
        }

        /// <summary>
        /// Create or Edit TrangThai
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/trang-thai")]
        public async Task CreateOrEdit(CreateOrEditTrangThaiDto input)
        {
            if (input.Id == null)
            {
                await Create(input);
            }
            else
            {
                await Update(input);
            }
        }

        /// <summary>
        /// Create TrangThai
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Create(CreateOrEditTrangThaiDto input)
        {
            var item = ObjectMapper.Map<TrangThai>(input);
            if (AbpSession.TenantId != null)
            {
                item.TenantId = (int?)AbpSession.TenantId;
            }
            if (AbpSession.UserId != null)
            {
                item.created_by_id = AbpSession.UserId;
            }
            item.created_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            await _TrangThaisRepository.InsertAsync(item);
        }

        /// <summary>
        /// Update TrangThai
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Update(CreateOrEditTrangThaiDto input)
        {
            var item = await _TrangThaisRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == (int)input.Id);
            if (AbpSession.UserId != null)
            {
                item.updated_by_id = AbpSession.UserId;
            }
            item.updated_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            ObjectMapper.Map(input, item);
        }

        /// <summary>
        /// Delete TrangThai
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/trang-thai")]
        // [HttpPost]
        public async Task Delete(EntityDto input)
        {
            await _TrangThaisRepository.DeleteAsync(input.Id);
        }

        /// <summary>
        /// Get all TrangThai for table dropdown
        /// </summary>
        /// <returns></returns>
        [Route("api/services/app/trang-thai/list")]
        public async Task<List<TrangThaiOutput>> GetAllForTableDropdown()
        {
            return await _TrangThaisRepository.GetAll()
                .Where(e => e.published_at.HasValue)
                .OrderBy(e => e.thu_tu)
                .Select(item => new TrangThaiOutput
                {
                    id = item.Id,
                    ten = item.ten
                }).ToListAsync();
        }
    }
}