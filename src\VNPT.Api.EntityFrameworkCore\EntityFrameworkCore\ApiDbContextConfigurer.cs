using Microsoft.EntityFrameworkCore;
using System.Data.Common;

namespace VNPT.Api.EntityFrameworkCore
{
    public static class ApiDbContextConfigurer
    {
        public static void Configure(DbContextOptionsBuilder<ApiDbContext> builder, string connectionString)
        {
            // builder.UseSqlServer(connectionString);
            builder.UseNpgsql(connectionString);
        }

        public static void Configure(DbContextOptionsBuilder<ApiDbContext> builder, DbConnection connection)
        {
            // builder.UseSqlServer(connection);
            builder.UseNpgsql(connection);
        }
    }
}
