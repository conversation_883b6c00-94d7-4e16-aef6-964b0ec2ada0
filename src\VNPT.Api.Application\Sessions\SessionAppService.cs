﻿using Abp.Auditing;
using Abp.UI;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VNPT.Api.Authorization.Users;
using VNPT.Api.Sessions.Dto;
using VNPT.Api.Users.Dto;

namespace VNPT.Api.Sessions
{
    public class SessionAppService : ApiAppServiceBase, ISessionAppService
    {
        private readonly UserManager _userManager;

        public SessionAppService(UserManager userManager)
        {
            _userManager = userManager;
        }
        [DisableAuditing]
        public async Task<GetCurrentLoginInformationsOutput> GetCurrentLoginInformations()
        {
            var output = new GetCurrentLoginInformationsOutput
            {
                Application = new ApplicationInfoDto
                {
                    Version = AppVersionHelper.Version,
                    ReleaseDate = AppVersionHelper.ReleaseDate,
                    Features = new Dictionary<string, bool>()
                }
            };

            if (AbpSession.TenantId.HasValue)
            {
                output.Tenant = ObjectMapper.Map<TenantLoginInfoDto>(await GetCurrentTenantAsync());
            }

            if (AbpSession.UserId.HasValue)
            {
                output.User = ObjectMapper.Map<UserLoginInfoDto>(await GetCurrentUserAsync());

                var user = await UserManager.GetUserByIdAsync(AbpSession.UserId.Value);
                output.GrantedPermissions = (await UserManager.GetGrantedPermissionsAsync(user))
                    .Select(p => p.Name).ToList();

                output.Roles = (await UserManager.GetRolesAsync(user)).ToList();
            }

            return output;
        }

        [HttpPost]
        public async Task<IdentityResult> UpdateCurrentLoginInformations(UserDto input)
        {
            if (AbpSession.UserId.HasValue && AbpSession.UserId.Value == input.Id)
            {
                var user = await _userManager.GetUserByIdAsync(input.Id);
                user.Name = input.Name;
                user.EmailAddress = input.EmailAddress;
                user.PhoneNumber = input.PhoneNumber;
                user.IsTwoFactorEnabled = input.IsTwoFactorEnabled;

                return await _userManager.UpdateAsync(user);
            }
            throw new UserFriendlyException("User not Login!");
        }
    }
}
