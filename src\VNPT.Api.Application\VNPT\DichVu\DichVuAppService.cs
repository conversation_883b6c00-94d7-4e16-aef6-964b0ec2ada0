﻿using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Authorization;
using VNPT.Api.Entities;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for DichVu
    /// </summary>
    [AbpAuthorize(PermissionNames.Pages_DichVu)]
    public class DichVuAppService : ApiAppServiceBase
    {
        private readonly IRepository<DichVu> _DichVusRepository;

        /// <summary>
        /// Service for DichVu
        /// </summary>
        public DichVuAppService(IRepository<DichVu> DichVusRepository)
        {
            _DichVusRepository = DichVusRepository;
        }

        /// <summary>
        /// Get all DichVu
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/dich-vu")]
        public async Task<PagedResultDto<GetDichVuOutput>> GetAll(GetAllDichVusInput input)
        {
            var filtered = _DichVusRepository.GetAll()
                        .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false || e.ma.ToLower().Contains(input.keyword.ToLower()) || e.ten.ToLower().Contains(input.keyword.ToLower()))
                        .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue);

            input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
            var pagedAndFiltered = filtered
                .OrderBy(input.Sorting ?? "id asc")
                .PageBy(input);

            var totalCount = await filtered.CountAsync();
            var results = new List<GetDichVuOutput>();
            foreach (var item in pagedAndFiltered)
            {
                var output = ObjectMapper.Map<GetDichVuOutput>(item);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return new PagedResultDto<GetDichVuOutput>(
                totalCount,
                results
            );

        }

        /// <summary>
        /// Get DichVu by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/services/app/dich-vu/{id}")]
        public async Task<GetDichVuOutput> Get(int id)
        {
            var item = await _DichVusRepository.FirstOrDefaultAsync(e => e.Id == id);
            if (item == null)
            {
                throw new UserFriendlyException("id does not exist");
            }
            var output = ObjectMapper.Map<GetDichVuOutput>(item);
            output.tinh_trang = item.published_at.HasValue;
            return output;
        }

        /// <summary>
        /// Create or Edit DichVu
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/dich-vu")]
        public async Task CreateOrEdit(CreateOrEditDichVuDto input)
        {
            if (input.Id == null)
            {
                await Create(input);
            }
            else
            {
                await Update(input);
            }
        }

        /// <summary>
        /// Create DichVu
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Create(CreateOrEditDichVuDto input)
        {
            var item = ObjectMapper.Map<DichVu>(input);
            if (AbpSession.TenantId != null)
            {
                item.TenantId = (int?)AbpSession.TenantId;
            }
            if (AbpSession.UserId != null)
            {
                item.created_by_id = AbpSession.UserId;
            }
            item.created_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            await _DichVusRepository.InsertAsync(item);
        }

        /// <summary>
        /// Update DichVu
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Update(CreateOrEditDichVuDto input)
        {
            var item = await _DichVusRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == (int)input.Id);
            if (AbpSession.UserId != null)
            {
                item.updated_by_id = AbpSession.UserId;
            }
            item.updated_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            ObjectMapper.Map(input, item);
        }

        /// <summary>
        /// Delete DichVu
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/dich-vu")]
        // [HttpPost]
        public async Task Delete(EntityDto input)
        {
            await _DichVusRepository.DeleteAsync(input.Id);
        }

        /// <summary>
        /// Get all DichVu for table dropdown
        /// </summary>
        /// <returns></returns>
        [Route("api/services/app/dich-vu/list")]
        public async Task<List<DichVuOutput>> GetAllForTableDropdown()
        {
            return await _DichVusRepository.GetAll()
                .Where(e => e.published_at.HasValue)
                .OrderBy(e => e.thu_tu)
                .Select(item => new DichVuOutput
                {
                    id = item.Id,
                    ten = item.ten
                }).ToListAsync();
        }
    }
}