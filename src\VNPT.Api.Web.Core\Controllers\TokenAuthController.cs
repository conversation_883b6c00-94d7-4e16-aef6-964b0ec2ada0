using Abp.Authorization;
using Abp.Authorization.Users;
using Abp.Domain.Repositories;
using Abp.MultiTenancy;
using Abp.Runtime.Caching;
using Abp.Runtime.Security;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RestSharp;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Threading.Tasks;
using VNPT.Api.Authentication.JwtBearer;
using VNPT.Api.Authorization;
using VNPT.Api.Authorization.Users;
using VNPT.Api.Entities;
using VNPT.Api.Models.TokenAuth;
using VNPT.Api.MultiTenancy;

namespace VNPT.Api.Controllers
{
    [Route("api/[controller]/[action]")]
    public class TokenAuthController : ApiControllerBase
    {
        private readonly LogInManager _logInManager;
        private readonly ITenantCache _tenantCache;
        private readonly AbpLoginResultTypeHelper _abpLoginResultTypeHelper;
        private readonly TokenAuthConfiguration _configuration;
        private readonly ICacheManager _cacheManager;
        private readonly IRepository<CauHinh> _CauHinhsRepository;
        public TokenAuthController(
            LogInManager logInManager,
            ITenantCache tenantCache,
            AbpLoginResultTypeHelper abpLoginResultTypeHelper,
            TokenAuthConfiguration configuration,
            ICacheManager cacheManager,
            IRepository<CauHinh> cauHinhsRepository)
        {
            _logInManager = logInManager;
            _tenantCache = tenantCache;
            _abpLoginResultTypeHelper = abpLoginResultTypeHelper;
            _configuration = configuration;
            _cacheManager = cacheManager;
            _CauHinhsRepository = cauHinhsRepository;
        }

        [HttpPost]
        public async Task<AuthenticateResultModel> Authenticate([FromBody] AuthenticateModel model)
        {if (model.XacThucTapDoan == true)
            {
                var options = new RestClientOptions("https://api-onebss.vnpt.vn")
                {
                    MaxTimeout = -1,
                };
                var client = new RestClient(options);
                var request = new RestRequest("/quantri/user/xacthuc_tapdoan", Method.Post);
                request.AddHeader("Content-Type", "application/json");
                var body = @"{
" + "\n" +
                @"    ""username"": ""vopt.lan"",
" + "\n" +
                @"    ""password"": ""Vnpt@222""
" + "\n" +
                @"}";
                request.AddStringBody(body, DataFormat.Json);
                RestResponse response = await client.ExecuteAsync(request);
                Console.WriteLine(response.Content);
            }
            else
            {
                var loginResult = await GetLoginResultAsync(
                    model.UserNameOrEmailAddress,
                    model.Password,
                    GetTenancyNameOrNull()
                );
                if (loginResult.User.IsTwoFactorEnabled == true)
                {
                    // Nếu OTP không tồn tại trong cache thì sinh mới 
                    var cacheKey = $"OTP_{model.UserNameOrEmailAddress}";
                    if (string.IsNullOrEmpty(model.OTP))
                    {
                        // Sinh OTP 6 số ngẫu nhiên
                        var random = new Random();
                        var otp = random.Next(100000, 999999).ToString();
                        _cacheManager.GetCache("OTP").Set(cacheKey, otp, TimeSpan.FromMinutes(5));
                        // Gửi OTP qua telegram
                        var number = loginResult.User.PhoneNumber;
                        var bot = await _CauHinhsRepository.GetAll()
                            .Where(e => e.ma == "bot_telegram")
                            .FirstOrDefaultAsync();
                        if (bot == null)
                        {
                            throw new UserFriendlyException("Config bot_telegram does not exist");
                        }
                        var proxy_address = await _CauHinhsRepository.GetAll()
                            .Where(e => e.ma == "proxy_address")
                            .FirstOrDefaultAsync();
                        if (proxy_address == null)
                        {
                            throw new UserFriendlyException("Config proxy_address does not exist");
                        }
                        var proxy_username = await _CauHinhsRepository.GetAll()
                            .Where(e => e.ma == "proxy_username")
                            .FirstOrDefaultAsync();
                        if (proxy_username == null)
                        {
                            throw new UserFriendlyException("Config proxy_username does not exist");
                        }
                        var address = proxy_address.gia_tri;
                        var credentials = proxy_username.gia_tri.Split(':');
                        if (credentials.Length != 2)
                        {
                            throw new UserFriendlyException("Config proxy_username is invalid");
                        }
                        // Tạo HttpClient với proxy
                        var proxy = new WebProxy(address)
                        {
                            Credentials = new NetworkCredential(credentials[0], credentials[1])
                        };
                        var httpClientHandler = new HttpClientHandler()
                        {
                            Proxy = proxy,
                            UseProxy = true
                        };

                        using (var client = new HttpClient(httpClientHandler))
                        {
                            var request = new HttpRequestMessage(HttpMethod.Get, $"https://api.telegram.org/{bot.gia_tri}/sendMessage?chat_id={number}&text=Mã OTP của bạn là: {otp}");
                            var response = await client.SendAsync(request);
                            if (!response.IsSuccessStatusCode)
                            {
                                throw new UserFriendlyException(L("ErrorSendingOTP"));
                            }
                            return new AuthenticateResultModel
                            {
                                IsTwoFactorEnabled = loginResult.User.IsTwoFactorEnabled,
                            };
                        }
                    }
                    else
                    {
                        // var cacheValue = "123456"; // demo
                        var cacheValue = _cacheManager.GetCache("OTP").GetOrDefault(cacheKey) as string;
                        if (cacheValue != model.OTP)
                        {
                            throw new UserFriendlyException(L("InvalidTwoFactorCode"));
                        }
                    }
                }
            }
            
            var accessToken = CreateAccessToken(CreateJwtClaims(loginResult.Identity));

            return new AuthenticateResultModel
            {
                AccessToken = accessToken,
                EncryptedAccessToken = GetEncryptedAccessToken(accessToken),
                ExpireInSeconds = (int)_configuration.Expiration.TotalSeconds,
            };
        }

        private string GetTenancyNameOrNull()
        {
            if (!AbpSession.TenantId.HasValue)
            {
                return null;
            }

            return _tenantCache.GetOrNull(AbpSession.TenantId.Value)?.TenancyName;
        }

        private async Task<AbpLoginResult<Tenant, User>> GetLoginResultAsync(string usernameOrEmailAddress, string password, string tenancyName)
        {
            var loginResult = await _logInManager.LoginAsync(usernameOrEmailAddress, password, tenancyName);

            switch (loginResult.Result)
            {
                case AbpLoginResultType.Success:
                    return loginResult;
                default:
                    throw _abpLoginResultTypeHelper.CreateExceptionForFailedLoginAttempt(loginResult.Result, usernameOrEmailAddress, tenancyName);
            }
        }

        private string CreateAccessToken(IEnumerable<Claim> claims, TimeSpan? expiration = null)
        {
            var now = DateTime.UtcNow;

            var jwtSecurityToken = new JwtSecurityToken(
                issuer: _configuration.Issuer,
                audience: _configuration.Audience,
                claims: claims,
                notBefore: now,
                expires: now.Add(expiration ?? _configuration.Expiration),
                signingCredentials: _configuration.SigningCredentials
            );

            return new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);
        }

        private static List<Claim> CreateJwtClaims(ClaimsIdentity identity)
        {
            var claims = identity.Claims.ToList();
            var nameIdClaim = claims.First(c => c.Type == ClaimTypes.NameIdentifier);

            // Specifically add the jti (random nonce), iat (issued timestamp), and sub (subject/user) claims.
            claims.AddRange(new[]
            {
                new Claim(JwtRegisteredClaimNames.Sub, nameIdClaim.Value),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.Now.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
            });

            return claims;
        }

        private string GetEncryptedAccessToken(string accessToken)
        {
            return SimpleStringCipher.Instance.Encrypt(accessToken);
        }

        [HttpPost]
        public async Task LogOut()
        {
            if (AbpSession.UserId != null)
            {
                AbpSession = null;
            }
        }
    }
}
