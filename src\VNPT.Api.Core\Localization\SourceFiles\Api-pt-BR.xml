﻿<?xml version="1.0" encoding="utf-8" ?>
<localizationDictionary culture="pt-BR">
  <texts>
    <text name="HomePage" value="Página Inicial" />
    <text name="About" value="Sobre" />
    <text name="WelcomeMessage" value="Bem Vindo ao Api!" />
    <text name="FormIsNotValidMessage" value="Formulário não é válido. Por favor, verifique e corrija os erros." />
    <text name="TenantNameCanNotBeEmpty" value="Tenant não pode estar vazio" />
    <text name="InvalidUserNameOrPassword" value="Nome de usuário ou senha inválida" />
    <text name="ThereIsNoTenantDefinedWithName{0}" value="Não há Tenant definido com o nome {0}" />
    <text name="TenantIsNotActive" value="Tenant {0} não está ativo." />
    <text name="UserIsNotActiveAndCanNotLogin" value="Usuário {0} não está ativo e não pode logar." />
    <text name="UserEmailIsNotConfirmedAndCanNotLogin">Seu email não foi confirmado. Você não pode fazer login.</text>
    <text name="UserLockedOutMessage">Usuário bloqueado. Por favor, tente mais tarde.</text>
    <text name="PleaseEnterLoginInformation" value="Por favor insira as informações de login" />
    <text name="TenancyName" value="Nome da Tenancy" />
    <text name="UserNameOrEmail" value="Nome de usuário ou email" />
    <text name="Password" value="Senha" />
    <text name="RememberMe" value="Lembre de mim" />
    <text name="LogIn" value="Entrar" />
    <text name="LoginFailed" value="Falha no login!" />
    <text name="NameSurname" value="Nome sobrenome" />
    <text name="UserName" value="Nome de usuário" />
    <text name="Name" value="Nome" />
    <text name="Surname" value="Sobrenome" />
    <text name="EmailAddress" value="Endereço de e-mail" />
    <text name="Tenants" value="Tenants" />
    <text name="SavedSuccessfully" value="Salvo com sucesso" />
    <text name="CreateNewTenant" value="Crie um novo tenant" />
    <text name="AdminEmailAddress" value="Endereço de e-mail administrador" />
    <text name="Save" value="Salvar" />
    <text name="Cancel" value="Cancelar" />
    <text name="TenantName_Regex_Description" value="Tenant deve ter no mínimo 2 caracteres, começa com uma letra e continuar com letra, número, traço ou sublinhado." />
    <text name="DefaultPasswordIs" value="Senha padrão é {0}" />
    <text name="CanBeEmptyToLoginAsHost" value="Pode estar vazio para acessar como Host." />
    <text name="Register" value="Registrar" />
    <text name="OrLoginWith" value="Ou faça o login com" />
    <text name="WaitingForActivationMessage" value="A sua conta está esperando para ser ativado pelo administrador do sistema." />
    <text name="TenantSelection" value="Seleção de Tenant" />
    <text name="TenantSelection_Detail" value="Por favor selecione um dos seguintes tenants." />
    <text name="Logout" value="Sair" />
    <text name="RegisterFormUserNameInvalidMessage">Por favor, não entre um email no nome de usuário.</text>
    <text name="DatabaseConnectionString" value="Conexão com o banco de dados (Connection String)" />
    <text name="Users" value="Usuários" />
    <text name="IsActive" value="Ativo" />
    <text name="FullName" value="Nome completo" />
    <text name="CreateNewUser" value="Novo Usuário" />
    <text name="Yes" value="Sim" />
    <text name="No" value="Não" />
    <text name="Optional" value="Opcional" />
    <text name="LeaveEmptyToSwitchToHost">Deixe em branco para mudar para o host.</text>
    <text name="CurrentTenant">Tenant atual</text>
    <text name="NotSelected">Não selecionado</text>
    <text name="Change">Alterar</text>
    <text name="ChangeTenant">Alterar tenant</text>
    <text name="MultiLevelMenu">Menu multinível</text>
    <text name="Back">Voltar</text>
    <text name="SuccessfullyRegistered">Registrado com sucesso</text>
    <text name="WaitingForEmailActivation">Seu endereço de email deve ser ativado</text>
    <text name="Roles">Funções</text>
    <text name="DisplayName">Nome de apresentação</text>
    <text name="Edit">Editar</text>
    <text name="Delete">Excluir</text>
    <text name="CreateNewRole">Criar nova função</text>
    <text name="RoleName">Nome da função</text>
    <text name="Actions">Ações</text>
    <text name="CouldNotCompleteLoginOperation">Operação de login não foi completada. Tente novamente mais tarde.</text>
    <text name="CouldNotValidateExternalUser">Não foi possível validar um usuário externo</text>
    <text name="EditRole">Editar Função</text>
    <text name="EditTenant">Editar tenant</text>
    <text name="EditUser">Editar usuário</text>
    <text name="TenantIdIsNotActive{0}">TenantId {0} não está ativo</text>
    <text name="UnknownTenantId{0}">TenantId {0} não foi encontrado</text>
    <text name="ThisFieldIsRequired">Campo obrigatório</text>
    <text name="PleaseWait">Aguarde...</text>
    <text name="Administration">Administração</text>
    <text name="ClearAll">Limpar todos</text>
    <text name="ClearOthers">Limpar outros</text>
    <text name="LabelOptions">Opções de Legenda/Rótulo</text>
    <text name="Permissions">Permissões</text>
    <text name="RoleDescription">Descrição da função</text>
    <text name="Refresh">Atualizar</text>
    <text name="Create">Criar</text>
    <text name="UserDetails">Detalhes do usuário</text>
    <text name="UserRoles">Funções de usuário</text>
    <text name="ConfirmPassword">Confirme a senha</text>
    <text name="Version">Versão</text>
    <text name="On">Ligado</text>
    <text name="Off">Desligado</text>
    <text name="AreYouSureWantToDelete">Tem certeza que deseja excluir {0}?</text>
    <text name="SuccessfullyDeleted">Excluído com sucesso</text>
    <text name="StartTyping">Comece a escrever</text>
    <text name="Skins">Temas</text>
    <text name="Settings">Configurações</text>
    <text name="Search">Procurar</text>
    <text name="UpdatePassword">Alterar Senha</text>
    <text name="ResetPassword">Resetar Senha</text>
    <text name="Filter">Filtrar</text>
    <text name="All">Todos</text>
    <text name="CurrentPassword">Senha Atual</text>
    <text name="NewPassword">Nova Senha</text>
    <text name="ConfirmNewPassword">Confirme a Nova Senha</text>
    <text name="PasswordsDoNotMatch">As senhas são diferentes</text>
    <text name="PasswordsMustBeAtLeast8CharactersContainLowercaseUppercaseNumber">As senhas precisam ter no mínimo 8 caracteres, contendo pelo menos uma letra minúscula, uma maiúscula e um número</text>
    <text name="UserDeleteWarningMessage">O usuário {0} será excluído.</text>
    <text name="RoleDeleteWarningMessage">A função {0} será excluída e desassociada de todos os usuários pertencentes a ela.</text>
    <text name="TenantDeleteWarningMessage">O Tenant {0} será excluído.</text>
    <text name="SearchWithThreeDot">Pesquisar...</text>
  </texts>
</localizationDictionary>
