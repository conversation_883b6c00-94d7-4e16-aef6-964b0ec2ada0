﻿using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Authorization;
using VNPT.Api.Entities;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for Huyen
    /// </summary>
    [AbpAuthorize(PermissionNames.Pages_Huyen)]
    public class HuyenAppService : ApiAppServiceBase
    {
        private readonly IRepository<Huyen> _HuyensRepository;

        /// <summary>
        /// Service for Huyen
        /// </summary>
        public HuyenAppService(IRepository<Huyen> HuyensRepository)
        {
            _HuyensRepository = HuyensRepository;
        }

        /// <summary>
        /// Get all Huyen
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/huyen")]
        public async Task<PagedResultDto<GetHuyenOutput>> GetAll(GetAllHuyensInput input)
        {
            var filtered = _HuyensRepository.GetAll()
                        .Include(e => e.TinhFk)
                        .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false || e.ma.ToLower().Contains(input.keyword.ToLower()) || e.ten.ToLower().Contains(input.keyword.ToLower()))
                        .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue);

            input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
            var pagedAndFiltered = filtered
                .OrderBy(input.Sorting ?? "id asc")
                .PageBy(input);

            var totalCount = await filtered.CountAsync();
            var results = new List<GetHuyenOutput>();
            foreach (var item in pagedAndFiltered)
            {
                var output = ObjectMapper.Map<GetHuyenOutput>(item);
                output.tinh = ObjectMapper.Map<TinhOutput>(item.TinhFk);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return new PagedResultDto<GetHuyenOutput>(
                totalCount,
                results
            );

        }

        /// <summary>
        /// Get Huyen by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/services/app/huyen/{id}")]
        public async Task<GetHuyenOutput> Get(int id)
        {
            var item = await _HuyensRepository.GetAll()
                    .Include(e => e.TinhFk)
                    .FirstOrDefaultAsync(e => e.Id == id);
            if (item == null)
            {
                throw new UserFriendlyException("id does not exist");
            }
            var output = ObjectMapper.Map<GetHuyenOutput>(item);
            output.tinh = ObjectMapper.Map<TinhOutput>(item.TinhFk);
            output.tinh_trang = item.published_at.HasValue;
            return output;
        }

        /// <summary>
        /// Create or Edit Huyen
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/huyen")]
        public async Task CreateOrEdit(CreateOrEditHuyenDto input)
        {
            if (input.Id == null)
            {
                await Create(input);
            }
            else
            {
                await Update(input);
            }
        }

        /// <summary>
        /// Create Huyen
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Create(CreateOrEditHuyenDto input)
        {
            var item = ObjectMapper.Map<Huyen>(input);
            if (AbpSession.TenantId != null)
            {
                item.TenantId = (int?)AbpSession.TenantId;
            }
            if (AbpSession.UserId != null)
            {
                item.created_by_id = AbpSession.UserId;
            }
            item.created_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            await _HuyensRepository.InsertAsync(item);
        }

        /// <summary>
        /// Update Huyen
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Update(CreateOrEditHuyenDto input)
        {
            var item = await _HuyensRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == (int)input.Id);
            if (AbpSession.UserId != null)
            {
                item.updated_by_id = AbpSession.UserId;
            }
            item.updated_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            ObjectMapper.Map(input, item);
        }

        /// <summary>
        /// Delete Huyen
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/huyen")]
        // [HttpPost]
        public async Task Delete(EntityDto input)
        {
            await _HuyensRepository.DeleteAsync(input.Id);
        }

        /// <summary>
        /// Get all Huyen for table dropdown
        /// </summary>
        /// <returns></returns>
        [Route("api/services/app/huyen/list")]
        public async Task<List<HuyenOutput>> GetAllForTableDropdown()
        {
            return await _HuyensRepository.GetAll()
                .Where(e => e.published_at.HasValue)
                .OrderBy(e => e.thu_tu)
                .Select(item => new HuyenOutput
                {
                    id = item.Id,
                    ten = item.ten,
                    tinh_id = item.tinh_id,
                }).ToListAsync();
        }
    }
}