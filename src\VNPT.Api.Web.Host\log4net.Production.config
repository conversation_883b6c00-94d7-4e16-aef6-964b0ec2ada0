﻿<?xml version="1.0" encoding="utf-8"?>

<log4net>
  <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
    <file value="App_Data/Logs/Logs.txt" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="10" />
    <maximumFileSize value="10000KB" />
    <staticLogFileName value="true" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%-5level %date [%-5.5thread] %-40.40logger - %message%newline" />
    </layout>
  </appender>
  <appender name="SentryAppender" type="Sentry.Log4Net.SentryAppender, Sentry.Log4Net">
    <!--Defining the DSN here allows log4net integration to initialize the SDK-->
    <!--You can leave the DSN out of this configuration file and initialize the SDK-->
    <!--via code (SentrySdk.Init) or via another integration like ASP.NET-->
    <!--The SDK only needs to be initialized once, you can choose where to do that-->
    <Dsn value="https://<EMAIL>/4504717342998528" />
    <!--Sends the log event Identity value as the user-->
    <SendIdentity value="true" />
    <threshold value="ERROR" />
  </appender>
  <root>
    <appender-ref ref="SentryAppender" />
    <appender-ref ref="RollingFileAppender" />
    <level value="ERROR" />
  </root>
</log4net>