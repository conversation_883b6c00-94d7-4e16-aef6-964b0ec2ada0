﻿using Abp.Application.Services.Dto;
using System;

namespace VNPT.Api.Applications.Dtos
{
    public class CreateOrEditThueBaoDto : EntityDto<int?>
    {
        public string trung_tam { get; set; }
        public string to_ky_thuat { get; set; }
        public string dia_ban { get; set; }
        public string nhan_vien_ky_thuat { get; set; }
        public string phong_ban_hang { get; set; }
        public string nhan_vien_kinh_doanh { get; set; }
        public string ma_thue_bao { get; set; }
        public string ma_thanh_toan { get; set; }
        public string ten_thue_bao { get; set; }
        public int? tuoi_thue_bao { get; set; }
        public int? dich_vu_id { get; set; }
        public int? doanh_thu_phat_sinh { get; set; }
        public string thiet_bi_dau_cuoi { get; set; }
        public string serial_number { get; set; }
        public string toc_do { get; set; }
        public int? toc_do_thuc_te { get; set; }
        public DateTime? ngay_su_dung { get; set; }
        public int? trang_thai_id { get; set; }
        public float? kinh_do { get; set; }
        public float? vi_do { get; set; }
        public string dia_chi_thanh_toan { get; set; }
        public string dia_chi_lap_dat { get; set; }
        public int? tinh_id { get; set; }
        public int? huyen_id { get; set; }
        public int? xa_id { get; set; }
        public string pho { get; set; }
        public string ap { get; set; }
        public string khu_pho { get; set; }
        public string so_nha { get; set; }
        public string ket_cuoi { get; set; }
        public string vi_tri { get; set; }
        public bool? tinh_trang { get; set; }
    }
}

