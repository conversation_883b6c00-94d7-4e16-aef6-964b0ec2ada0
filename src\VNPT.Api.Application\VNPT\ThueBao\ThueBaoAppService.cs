﻿using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.EntityFrameworkCore.Repositories;
using Abp.Linq.Extensions;
using Abp.Runtime.Caching;
using Abp.Runtime.Session;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Authorization;
using VNPT.Api.Authorization.Roles;
using VNPT.Api.Authorization.Users;
using VNPT.Api.Entities;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for ThueBao
    /// </summary>
    [AbpAuthorize(PermissionNames.Pages_ThueBao)]
    public class ThueBaoAppService : ApiAppServiceBase
    {
        private readonly IRepository<ThueBao> _ThueBaosRepository;
        private readonly ICacheManager _cacheManager;
        private readonly UserManager _userManager;

        /// <summary>
        /// Service for ThueBao
        /// </summary>
        public ThueBaoAppService(IRepository<ThueBao> ThueBaosRepository, ICacheManager cacheManager, UserManager userManager)
        {
            _ThueBaosRepository = ThueBaosRepository;
            _cacheManager = cacheManager;
            _userManager = userManager;
        }

        /// <summary>
        /// Get all ThueBao
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/thue-bao")]
        public async Task<PagedResultDto<GetThueBaoOutput>> GetAll(GetAllThueBaoInput input)
        {
            if (AbpSession.UserId == null)
            {
                throw new UserFriendlyException("There is no current user!");
            }
            var currentUser = await _userManager.GetUserByIdAsync(AbpSession.GetUserId());
            var roles = await _userManager.GetRolesAsync(currentUser);
            var isManager = roles.Contains("Lanh_Dao");
            var scope = currentUser.Surname.ToLower();

            var filtered = _ThueBaosRepository.GetAll()
                        .Include(e => e.DichVuFk)
                        .Include(e => e.TrangThaiFk)
                        .Include(e => e.TinhFk)
                        .Include(e => e.HuyenFk)
                        .Include(e => e.XaFk)
                        .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false
                            || e.ma_thue_bao.ToLower().Contains(input.keyword.ToLower())
                            || e.ma_thanh_toan.ToLower().Contains(input.keyword.ToLower())
                            || e.ten_thue_bao.ToLower().Contains(input.keyword.ToLower())
                            || e.nhan_vien_ky_thuat.ToLower().Contains(input.keyword.ToLower())
                            || e.nhan_vien_kinh_doanh.ToLower().Contains(input.keyword.ToLower()))
                        .WhereIf(!string.IsNullOrWhiteSpace(input.trung_tam), e => input.trung_tam.ToLower().Contains(e.trung_tam.ToLower()))
                        .WhereIf(!string.IsNullOrWhiteSpace(input.to_ky_thuat), e => input.to_ky_thuat.ToLower().Contains(e.to_ky_thuat.ToLower()))
                        .WhereIf(!string.IsNullOrWhiteSpace(input.dia_ban), e => input.dia_ban.ToLower().Contains(e.dia_ban.ToLower()))
                        .WhereIf(!string.IsNullOrWhiteSpace(input.nhan_vien_ky_thuat), e => input.nhan_vien_ky_thuat.ToLower().Contains(e.nhan_vien_ky_thuat.ToLower()))
                        .WhereIf(!string.IsNullOrWhiteSpace(input.phong_ban_hang), e => input.phong_ban_hang.ToLower().Contains(e.phong_ban_hang.ToLower()))
                        .WhereIf(!string.IsNullOrWhiteSpace(input.nhan_vien_kinh_doanh), e => input.nhan_vien_kinh_doanh.ToLower().Contains(e.nhan_vien_kinh_doanh.ToLower()))
                        .WhereIf(input.dich_vu_id.HasValue, e => e.dich_vu_id == input.dich_vu_id)
                        .WhereIf(input.trang_thai_id.HasValue, e => e.trang_thai_id == input.trang_thai_id)
                        .WhereIf(input.tinh_id.HasValue, e => e.tinh_id == input.tinh_id)
                        .WhereIf(input.huyen_id.HasValue, e => e.huyen_id == input.huyen_id)
                        .WhereIf(input.xa_id.HasValue, e => e.xa_id == input.xa_id)
                        .WhereIf(!string.IsNullOrWhiteSpace(input.ap_khu_pho),
                            e => e.dia_chi_lap_dat.ToLower().Contains(input.ap_khu_pho.ToLower())
                            || e.ap.ToLower() == input.ap_khu_pho.ToLower()
                            || e.khu_pho.ToLower() == input.ap_khu_pho.ToLower())
                        .WhereIf(input.doanh_thu_tu.HasValue, e => e.doanh_thu_phat_sinh >= input.doanh_thu_tu)
                        .WhereIf(input.doanh_thu_den.HasValue, e => e.doanh_thu_phat_sinh <= input.doanh_thu_den)
                        .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue)
                        .WhereIf(!isManager, e => scope.Contains(e.trung_tam.ToLower()));

            // input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
            var pagedAndFiltered = filtered
                .OrderBy(input.Sorting ?? "id asc")
                .PageBy(input);

            var totalCount = await filtered.CountAsync();
            var results = new List<GetThueBaoOutput>();
            foreach (var item in pagedAndFiltered)
            {
                var output = ObjectMapper.Map<GetThueBaoOutput>(item);
                output.dich_vu = ObjectMapper.Map<DichVuOutput>(item.DichVuFk);
                output.trang_thai = ObjectMapper.Map<TrangThaiOutput>(item.TrangThaiFk);
                output.tinh = ObjectMapper.Map<TinhOutput>(item.TinhFk);
                output.huyen = ObjectMapper.Map<HuyenOutput>(item.HuyenFk);
                output.xa = ObjectMapper.Map<XaOutput>(item.XaFk);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return new PagedResultDto<GetThueBaoOutput>(
                totalCount,
                results
            );

        }

        /// <summary>
        /// Get all ThueBao for map
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/thue-bao/map")]
        public async Task<PagedResultDto<ThueBaoOutput>> GetAllForMap(GetAllThueBaoInput input)
        {
            if (AbpSession.UserId == null)
            {
                throw new UserFriendlyException("There is no current user!");
            }
            var currentUser = await _userManager.GetUserByIdAsync(AbpSession.GetUserId());
            var roles = await _userManager.GetRolesAsync(currentUser);
            var isManager = roles.Contains("Lanh_Dao");
            var scope = currentUser.Surname.ToLower();

            // Define a cache key based on input parameters
            var cacheKey = $"GetAllForMap_{input.keyword ?? "null"}_{input.trung_tam ?? "null"}_{input.to_ky_thuat ?? "null"}_{input.dia_ban ?? "null"}_{input.nhan_vien_ky_thuat ?? "null"}_{input.phong_ban_hang ?? "null"}_{input.nhan_vien_kinh_doanh ?? "null"}_{input.dich_vu_id?.ToString() ?? "null"}_{input.trang_thai_id?.ToString() ?? "null"}_{input.tinh_id?.ToString() ?? "null"}_{input.huyen_id?.ToString() ?? "null"}_{input.xa_id?.ToString() ?? "null"}_{input.ap_khu_pho ?? "null"}_{input.doanh_thu_tu?.ToString() ?? "null"}_{input.doanh_thu_den?.ToString() ?? "null"}_{input.status?.ToString() ?? "null"}";

            // Get typed cache instance
            var cache = _cacheManager.GetCache<string, PagedResultDto<ThueBaoOutput>>("ThueBaoMapCache");

            // Try to get from cache or create if not found
            return await cache.GetAsync(cacheKey, async () =>
            {
                var filtered = _ThueBaosRepository.GetAll()
                            .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false
                                || e.ma_thue_bao.ToLower().Contains(input.keyword.ToLower())
                                || e.ma_thanh_toan.ToLower().Contains(input.keyword.ToLower())
                                || e.ten_thue_bao.ToLower().Contains(input.keyword.ToLower())
                                || e.nhan_vien_ky_thuat.ToLower().Contains(input.keyword.ToLower())
                                || e.nhan_vien_kinh_doanh.ToLower().Contains(input.keyword.ToLower()))
                            .WhereIf(!string.IsNullOrWhiteSpace(input.trung_tam), e => input.trung_tam.ToLower().Contains(e.trung_tam.ToLower()))
                            .WhereIf(!string.IsNullOrWhiteSpace(input.to_ky_thuat), e => input.to_ky_thuat.ToLower().Contains(e.to_ky_thuat.ToLower()))
                            .WhereIf(!string.IsNullOrWhiteSpace(input.dia_ban), e => input.dia_ban.ToLower().Contains(e.dia_ban.ToLower()))
                            .WhereIf(!string.IsNullOrWhiteSpace(input.nhan_vien_ky_thuat), e => input.nhan_vien_ky_thuat.ToLower().Contains(e.nhan_vien_ky_thuat.ToLower()))
                            .WhereIf(!string.IsNullOrWhiteSpace(input.phong_ban_hang), e => input.phong_ban_hang.ToLower().Contains(e.phong_ban_hang.ToLower()))
                            .WhereIf(!string.IsNullOrWhiteSpace(input.nhan_vien_kinh_doanh), e => input.nhan_vien_kinh_doanh.ToLower().Contains(e.nhan_vien_kinh_doanh.ToLower()))
                            .WhereIf(input.dich_vu_id.HasValue, e => e.dich_vu_id == input.dich_vu_id)
                            .WhereIf(input.trang_thai_id.HasValue, e => e.trang_thai_id == input.trang_thai_id)
                            .WhereIf(input.tinh_id.HasValue, e => e.tinh_id == input.tinh_id)
                            .WhereIf(input.huyen_id.HasValue, e => e.huyen_id == input.huyen_id)
                            .WhereIf(input.xa_id.HasValue, e => e.xa_id == input.xa_id)
                            .WhereIf(!string.IsNullOrWhiteSpace(input.ap_khu_pho),
                                e => e.dia_chi_lap_dat.ToLower().Contains(input.ap_khu_pho.ToLower())
                                || e.ap.ToLower() == input.ap_khu_pho.ToLower()
                                || e.khu_pho.ToLower() == input.ap_khu_pho.ToLower())
                            .WhereIf(input.doanh_thu_tu.HasValue, e => e.doanh_thu_phat_sinh >= input.doanh_thu_tu)
                            .WhereIf(input.doanh_thu_den.HasValue, e => e.doanh_thu_phat_sinh <= input.doanh_thu_den)
                            .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue)
                            .WhereIf(!isManager, e => scope.Contains(e.trung_tam.ToLower()));

                // input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
                var pagedAndFiltered = filtered
                    .OrderBy(input.Sorting ?? "id asc")
                    .OrderBy(x => Guid.NewGuid())  // Sử dụng Guid.NewGuid() để randomize order
                    .PageBy(input);

                // var totalCount = await filtered.CountAsync();
                var results = new List<ThueBaoOutput>();
                foreach (var item in pagedAndFiltered)
                {
                    var output = new ThueBaoOutput
                    {
                        id = item.Id,
                        ma_thue_bao = item.ma_thue_bao,
                        tuoi_thue_bao = item.tuoi_thue_bao,
                        toc_do_thuc_te = item.toc_do_thuc_te,
                        doanh_thu_phat_sinh = item.doanh_thu_phat_sinh,
                        trung_tam = item.trung_tam,
                        dia_ban = item.dia_ban,
                        phong_ban_hang = item.phong_ban_hang,
                        nhan_vien_ky_thuat = item.nhan_vien_ky_thuat,
                        nhan_vien_kinh_doanh = item.nhan_vien_kinh_doanh,
                        kinh_do = item.kinh_do,
                        vi_do = item.vi_do,
                    };
                    results.Add(output);
                }

                return new PagedResultDto<ThueBaoOutput>(
                    results.Count,
                    results
                );
            });
        }

        /// <summary>
        /// Get ThueBao by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/services/app/thue-bao/{id}")]
        public async Task<GetThueBaoOutput> Get(int id)
        {
            var item = await _ThueBaosRepository.GetAll()
                        .Include(e => e.DichVuFk)
                        .Include(e => e.TrangThaiFk)
                        .Include(e => e.TinhFk)
                        .Include(e => e.HuyenFk)
                        .Include(e => e.XaFk)
                        .FirstOrDefaultAsync(e => e.Id == id);
            if (item == null)
            {
                throw new UserFriendlyException("id does not exist");
            }
            var output = ObjectMapper.Map<GetThueBaoOutput>(item);
            output.dich_vu = ObjectMapper.Map<DichVuOutput>(item.DichVuFk);
            output.trang_thai = ObjectMapper.Map<TrangThaiOutput>(item.TrangThaiFk);
            output.tinh = ObjectMapper.Map<TinhOutput>(item.TinhFk);
            output.huyen = ObjectMapper.Map<HuyenOutput>(item.HuyenFk);
            output.xa = ObjectMapper.Map<XaOutput>(item.XaFk);
            output.tinh_trang = item.published_at.HasValue;
            return output;
        }

        /// <summary>
        /// Import ThueBao
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        [Route("api/services/app/thue-bao/import")]
        [AbpAuthorize(PermissionNames.Command_ThueBao_Import)]
        public async Task Import(CreateOrEditThueBaoDto[] list)
        {
            foreach (var item in list)
            {
                var thueBao = await _ThueBaosRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.ma_thue_bao == item.ma_thue_bao);
                if (thueBao != null)
                {
                    item.Id = thueBao.Id;
                    await Update(item);
                }
                else
                {
                    await Create(item);
                }
            }
            // Clear all caches
            var caches = _cacheManager.GetAllCaches();
            foreach (var cache in caches)
            {
                cache.Clear();
            }
        }

        /// <summary>
        /// Create or Edit ThueBao
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/thue-bao")]
        public async Task CreateOrEdit(CreateOrEditThueBaoDto input)
        {
            if (input.Id == null)
            {
                await Create(input);
            }
            else
            {
                await Update(input);
            }
        }

        /// <summary>
        /// Create ThueBao
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Create(CreateOrEditThueBaoDto input)
        {
            var item = ObjectMapper.Map<ThueBao>(input);
            if (AbpSession.TenantId != null)
            {
                item.TenantId = (int?)AbpSession.TenantId;
            }
            if (AbpSession.UserId != null)
            {
                item.created_by_id = AbpSession.UserId;
            }
            item.created_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            await _ThueBaosRepository.InsertAsync(item);
        }

        /// <summary>
        /// Update ThueBao
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Update(CreateOrEditThueBaoDto input)
        {
            var item = await _ThueBaosRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == (int)input.Id);
            if (AbpSession.UserId != null)
            {
                item.updated_by_id = AbpSession.UserId;
            }
            item.updated_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            ObjectMapper.Map(input, item);
        }

        /// <summary>
        /// Delete ThueBao
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/thue-bao")]
        // [HttpPost]
        public async Task Delete(EntityDto input)
        {
            await _ThueBaosRepository.DeleteAsync(input.Id);
        }

        /// <summary>
        /// Delete ThueBao
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/thue-bao/delete-all")]
        // [HttpPost]
        public async Task DeleteAll(int input)
        {
            var all_thue_baos = _ThueBaosRepository.GetAll();
            if (all_thue_baos.Count() == input)
            {
                await _ThueBaosRepository.BatchDeleteAsync(e => e.Id > 0);
            }
        }
    }
}