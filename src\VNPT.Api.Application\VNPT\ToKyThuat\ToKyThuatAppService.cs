﻿using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Authorization;
using VNPT.Api.Entities;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for ToKyThuat
    /// </summary>
    [AbpAuthorize(PermissionNames.Pages_ToKyThuat)]
    public class ToKyThuatAppService : ApiAppServiceBase
    {
        private readonly IRepository<ToKyThuat> _ToKyThuatsRepository;

        /// <summary>
        /// Service for ToKyThuat
        /// </summary>
        public ToKyThuatAppService(IRepository<ToKyThuat> ToKyThuatsRepository)
        {
            _ToKyThuatsRepository = ToKyThuatsRepository;
        }

        /// <summary>
        /// Get all ToKyThuat
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/to-ky-thuat")]
        public async Task<PagedResultDto<GetToKyThuatOutput>> GetAll(GetAllToKyThuatsInput input)
        {
            var filtered = _ToKyThuatsRepository.GetAll()
                        .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false || e.ma.ToLower().Contains(input.keyword.ToLower()) || e.ten.ToLower().Contains(input.keyword.ToLower()))
                        .WhereIf(!string.IsNullOrWhiteSpace(input.trung_tam), e => input.trung_tam == e.trung_tam)
                        .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue);

            input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
            var pagedAndFiltered = filtered
                .OrderBy(input.Sorting ?? "id asc")
                .PageBy(input);

            var totalCount = await filtered.CountAsync();
            var results = new List<GetToKyThuatOutput>();
            foreach (var item in pagedAndFiltered)
            {
                var output = ObjectMapper.Map<GetToKyThuatOutput>(item);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return new PagedResultDto<GetToKyThuatOutput>(
                totalCount,
                results
            );

        }

        /// <summary>
        /// Get ToKyThuat by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/services/app/to-ky-thuat/{id}")]
        public async Task<GetToKyThuatOutput> Get(int id)
        {
            var item = await _ToKyThuatsRepository.FirstOrDefaultAsync(e => e.Id == id);
            if (item == null)
            {
                throw new UserFriendlyException("id does not exist");
            }
            var output = ObjectMapper.Map<GetToKyThuatOutput>(item);
            output.tinh_trang = item.published_at.HasValue;
            return output;
        }

        /// <summary>
        /// Create or Edit ToKyThuat
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/to-ky-thuat")]
        public async Task CreateOrEdit(CreateOrEditToKyThuatDto input)
        {
            if (input.Id == null)
            {
                await Create(input);
            }
            else
            {
                await Update(input);
            }
        }

        /// <summary>
        /// Create ToKyThuat
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Create(CreateOrEditToKyThuatDto input)
        {
            var item = ObjectMapper.Map<ToKyThuat>(input);
            if (AbpSession.TenantId != null)
            {
                item.TenantId = (int?)AbpSession.TenantId;
            }
            if (AbpSession.UserId != null)
            {
                item.created_by_id = AbpSession.UserId;
            }
            item.created_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            await _ToKyThuatsRepository.InsertAsync(item);
        }

        /// <summary>
        /// Update ToKyThuat
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Update(CreateOrEditToKyThuatDto input)
        {
            var item = await _ToKyThuatsRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == (int)input.Id);
            if (AbpSession.UserId != null)
            {
                item.updated_by_id = AbpSession.UserId;
            }
            item.updated_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            ObjectMapper.Map(input, item);
        }

        /// <summary>
        /// Delete ToKyThuat
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/to-ky-thuat")]
        // [HttpPost]
        public async Task Delete(EntityDto input)
        {
            await _ToKyThuatsRepository.DeleteAsync(input.Id);
        }

        /// <summary>
        /// Get all ToKyThuat for table dropdown
        /// </summary>
        /// <returns></returns>
        [Route("api/services/app/to-ky-thuat/list")]
        public async Task<List<ToKyThuatOutput>> GetAllForTableDropdown()
        {
            return await _ToKyThuatsRepository.GetAll()
                .Where(e => e.published_at.HasValue)
                .OrderBy(e => e.thu_tu)
                .Select(item => new ToKyThuatOutput
                {
                    id = item.Id,
                    ma = item.ma,
                    ten = item.ten,
                    trung_tam = item.trung_tam,
                }).ToListAsync();
        }
    }
}