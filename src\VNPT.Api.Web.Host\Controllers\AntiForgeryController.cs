using Abp.Web.Security.AntiForgery;
using Microsoft.AspNetCore.Antiforgery;
using VNPT.Api.Controllers;

namespace VNPT.Api.Web.Host.Controllers
{
    public class AntiForgeryController : ApiControllerBase
    {
        private readonly IAntiforgery _antiforgery;
        private readonly IAbpAntiForgeryManager _antiForgeryManager;

        public AntiForgeryController(IAntiforgery antiforgery, IAbpAntiForgeryManager antiForgeryManager)
        {
            _antiforgery = antiforgery;
            _antiForgeryManager = antiForgeryManager;
        }

        public void GetToken()
        {
            _antiforgery.SetCookieTokenAndHeader(HttpContext);
        }

        public void SetCookie()
        {
            // _antiForgeryManager.SetCookie(HttpContext);
        }
    }
}
