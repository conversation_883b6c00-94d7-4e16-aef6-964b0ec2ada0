<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AssetTargetFallback>$(AssetTargetFallback);portable-net45+win8+wp8+wpa81;</AssetTargetFallback>
    <PreserveCompilationContext>true</PreserveCompilationContext>
    <AssemblyName>VNPT.Api.Web.Host</AssemblyName>
    <OutputType>Exe</OutputType>
    <PackageId>VNPT.Api.Web.Host</PackageId>
    <UserSecretsId>VNPT-Api-56C2EF2F-ABD6-4EFC-AAF2-2E81C34E8FB1</UserSecretsId>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
    <LangVersion>7.2</LangVersion>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>
  <ItemGroup>
    <Content Remove="wwwroot\swagger\ui\index.html" />
  </ItemGroup>
  <ItemGroup>
    <None Update="Dockerfile">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="log4net.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
    <None Update="wwwroot\**\*">
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\VNPT.Api.Web.Core\VNPT.Api.Web.Core.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Abp.Castle.Log4Net" Version="9.4.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Sentry.Log4Net" Version="4.1.1" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="wwwroot\swagger\ui\index.html">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>
</Project>