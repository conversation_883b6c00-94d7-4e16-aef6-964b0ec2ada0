# Module <PERSON>hu<PERSON> - VNPT Customer Management

## <PERSON><PERSON> tả

Module Thuê Bao là module trung tâm của hệ thống quản lý khách hàng VNPT, cho phép quản lý toàn bộ thông tin thuê bao/khách hàng bao gồm thông tin cá nhân, k<PERSON>, địa lý và tổ chức.

## Cấu trúc module

### 1. Entity (\src\VNPT.Api.Core\VNPT\ThueBao.cs)

#### Thông tin cơ bản

- **ma_thue_bao**: <PERSON><PERSON> <PERSON>u<PERSON> bao (bắt buộc)
- **ma_thanh_toan**: Mã thanh toán
- **ten_thue_bao**: <PERSON><PERSON><PERSON> <PERSON>u<PERSON>ao (bắt buộc)
- **tuoi_thue_bao**: Tuổi thuê bao (tháng)
- **doanh_thu_phat_sinh**: <PERSON><PERSON><PERSON> thu phát sinh

#### Thông tin kỹ thuật

- **dich_vu_id**: ID dịch vụ đăng ký (FK)
- **trang_thai_id**: <PERSON> trạng thái thuê bao (FK)
- **thiet_bi_dau_cuoi**: Thiết bị đầu cuối
- **serial_number**: Số serial thiết bị
- **toc_do**: Tốc độ đăng ký
- **toc_do_thuc_te**: Tốc độ thực tế (Mbps)
- **ngay_su_dung**: Ngày bắt đầu sử dụng
- **ket_cuoi**: Kết cuối
- **vi_tri**: Vị trí chi tiết

#### Thông tin địa lý và vị trí

- **dia_chi_thanh_toan**: Địa chỉ thanh toán
- **dia_chi_lap_dat**: Địa chỉ lắp đặt
- **tinh_id**: ID tỉnh/thành (FK)
- **huyen_id**: ID huyện/quận (FK)
- **xa_id**: ID xã/phường (FK)
- **pho**: Tên phố
- **ap**: Tên ấp
- **khu_pho**: Tên khu phố
- **so_nha**: Số nhà
- **kinh_do**: Kinh độ (longitude)
- **vi_do**: Vĩ độ (latitude)

#### Thông tin tổ chức

- **trung_tam**: Tên trung tâm
- **to_ky_thuat**: Tên tổ kỹ thuật
- **dia_ban**: Tên địa bàn
- **nhan_vien_ky_thuat**: Tên nhân viên kỹ thuật
- **phong_ban_hang**: Tên phòng bán hàng
- **nhan_vien_kinh_doanh**: Tên nhân viên kinh doanh

- Kế thừa các trường chuẩn từ base entity (id, tenant_id, created_at, updated_at, published_at, created_by_id, updated_by_id)

### 2. DTOs (\src\VNPT.Api.Application\VNPT\ThueBao\Dtos\)

- **ThueBaoDto.cs**: DTO cơ bản
- **CreateOrEditThueBaoDto.cs**: DTO cho tạo/sửa
- **GetAllThueBaosInput.cs**: DTO cho tham số tìm kiếm
- **GetThueBaoOutput.cs**: DTO cho kết quả trả về
- **ListThueBaoOutput.cs**: DTO cho danh sách kết quả trả về

### 3. Application Services

- **ThueBaoAppService.cs**: Implementation service

## Các API Endpoints

### Danh sách thuê bao

```
GET /api/services/app/thue-bao
Parameters: keyword, status, tinhId, huyenId, xaId, dichVuId, trangThaiId, trungTam, toKyThuat, diaBan, phongBanHang, nhanVienKyThuat, nhanVienKinhDoanh, doanhThuFrom, doanhThuTo, sorting, skipCount, maxResultCount
```

### Danh sách thuê bao cho bản đồ

```
GET /api/services/app/thue-bao/for-map
Parameters: Tương tự GetAll nhưng trả về dữ liệu tối ưu cho hiển thị bản đồ
```

### Chi tiết thuê bao

```
GET /api/services/app/thue-bao/{id}
```

### Tạo/Cập nhật thuê bao

```
POST /api/services/app/thue-bao
Body: CreateOrEditThueBaoDto
```

### Xóa thuê bao

```
DELETE /api/services/app/thue-bao
Body: { id: number }
```

### Danh sách thuê bao cho dropdown

```
GET /api/services/app/thue-bao/list
```

### Import từ Excel

```
POST /api/services/app/thue-bao/import-excel
Body: File upload
```

### Export Excel

```
GET /api/services/app/thue-bao/export-excel
Parameters: Tương tự GetAll
```

## Cách sử dụng

### 1. Thiết lập quyền

Module sử dụng permission `Pages.ThueBao` đã được cấu hình trong:

- `PermissionNames.cs`
- `ApiAuthorizationProvider.cs`

### 2. Cấu hình AutoMapper

AutoMapper đã được cấu hình trong `CustomDtoMapper.cs`

### 3. Dependency Injection

Service đã được đăng ký tự động thông qua ABP Framework

### 4. Database Index

Đã có các index được tối ưu cho hiệu suất:

- Index trên `ma_thue_bao` (UNIQUE)
- Index trên các trường tìm kiếm phổ biến
- Index cho hiển thị bản đồ (kinh_do, vi_do)

## Ví dụ sử dụng API

### Tạo thuê bao mới

```json
POST /api/services/app/thue-bao
{
    "ma_thue_bao": "TB001",
    "ma_thanh_toan": "TT001",
    "ten_thue_bao": "Nguyễn Văn A",
    "tuoi_thue_bao": 24,
    "dich_vu_id": 1,
    "trang_thai_id": 1,
    "dia_chi_lap_dat": "123 Đường ABC, Phường XYZ",
    "tinh_id": 1,
    "huyen_id": 1,
    "xa_id": 1,
    "kinh_do": 106.6297,
    "vi_do": 10.8231,
    "trung_tam": "Trung tâm Quận 1",
    "to_ky_thuat": "Tổ KT 01",
    "tinh_trang": true
}
```

### Tìm kiếm thuê bao nâng cao

```
GET /api/services/app/thue-bao?keyword=Nguyễn&tinhId=1&dichVuId=1&doanhThuFrom=1000000&doanhThuTo=5000000&maxResultCount=10
```

### Lấy dữ liệu cho bản đồ

```
GET /api/services/app/thue-bao/for-map?tinhId=1&status=true
```

### Import Excel

```
POST /api/services/app/thue-bao/import-excel
Content-Type: multipart/form-data
Body: Excel file
```

## Tính năng chính

1. **Quản lý CRUD**: Tạo, đọc, cập nhật, xóa thuê bao
2. **Tìm kiếm nâng cao**: Theo nhiều tiêu chí khác nhau
   - Tìm kiếm theo keyword (mã, tên, địa chỉ)
   - Lọc theo vị trí địa lý (tỉnh, huyện, xã)
   - Lọc theo tổ chức (trung tâm, tổ kỹ thuật, địa bàn)
   - Lọc theo nhân viên (kỹ thuật, kinh doanh)
   - Lọc theo dịch vụ và trạng thái
   - Lọc theo khoảng doanh thu
3. **Hiển thị bản đồ**: Hiển thị vị trí thuê bao trên Google Maps
4. **Import/Export Excel**: Nhập và xuất dữ liệu hàng loạt
5. **Phân trang**: Hỗ trợ phân trang cho danh sách lớn
6. **Cache**: Sử dụng cache cho dữ liệu bản đồ
7. **Audit trail**: Theo dõi người tạo, cập nhật và thời gian
8. **Multi-tenant**: Hỗ trợ đa tenant

## Quan hệ với các module khác

1. **DichVu**: Thuê bao thuộc về một dịch vụ
2. **TrangThai**: Thuê bao có một trạng thái
3. **Tinh/Huyen/Xa**: Thuê bao có vị trí địa lý
4. **GhiChu**: Mỗi thuê bao có thể có nhiều ghi chú
5. **TrungTam/ToKyThuat/DiaBan**: Thuê bao thuộc cơ cấu tổ chức
6. **PhongBanHang/NhanVien**: Thuê bao được phụ trách bởi nhân viên

## Lưu ý kỹ thuật

1. **Foreign Keys**: Quan hệ với các bảng dich_vus, trang_thais, tinhs, huyens, xas
2. **String References**: Sử dụng trường text cho trung_tam, to_ky_thuat, dia_ban, nhân viên để tối ưu hiệu suất
3. **Geolocation**: Sử dụng kinh_do, vi_do cho hiển thị bản đồ
4. **Index**: Đã tối ưu index cho các truy vấn phổ biến
5. **Validation**: Mã thuê bao phải unique
6. **Authorization**: Sử dụng `[AbpAuthorize(PermissionNames.Pages_ThueBao)]`
7. **Soft Delete**: Sử dụng `published_at` để đánh dấu trạng thái
8. **Cache**: Sử dụng cache cho API `GetAllForMap`
9. **Performance**: Giới hạn MaxResultCount = 1000 để tránh overload

## Best Practices

1. **Tìm kiếm hiệu quả**: Sử dụng các index đã được tối ưu
2. **Bản đồ**: Sử dụng endpoint riêng cho dữ liệu bản đồ
3. **Import dữ liệu**: Kiểm tra duplicate theo mã thuê bao
4. **Phân trang**: Luôn sử dụng phân trang cho danh sách lớn
5. **Cache**: Tận dụng cache cho dữ liệu ít thay đổi
6. **Validation**: Validate dữ liệu trước khi save
7. **Error Handling**: Xử lý lỗi gracefully cho import/export
