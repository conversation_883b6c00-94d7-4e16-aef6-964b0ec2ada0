﻿using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Authorization;
using VNPT.Api.Authorization.Users;
using VNPT.Api.Entities;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for PhongBanHang
    /// </summary>
    [AbpAuthorize(PermissionNames.Pages_PhongBanHang)]
    public class PhongBanHangAppService : ApiAppServiceBase
    {
        private readonly UserManager _userManager;
        private readonly IRepository<PhongBanHang> _PhongBanHangsRepository;

        /// <summary>
        /// Service for PhongBanHang
        /// </summary>
        public PhongBanHangAppService(UserManager userManager, IRepository<PhongBanHang> PhongBanHangsRepository)
        {
            _userManager = userManager;
            _PhongBanHangsRepository = PhongBanHangsRepository;
        }

        /// <summary>
        /// Get all PhongBanHang
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/phong-ban-hang")]
        public async Task<PagedResultDto<GetPhongBanHangOutput>> GetAll(GetAllPhongBanHangsInput input)
        {
            if (AbpSession.UserId == null)
            {
                throw new UserFriendlyException("There is no current user!");
            }
            var currentUser = await _userManager.GetUserByIdAsync(AbpSession.GetUserId());
            var roles = await _userManager.GetRolesAsync(currentUser);
            var isManager = roles.Contains("Lanh_Dao");
            var scope = currentUser.Surname.ToLower();

            var filtered = _PhongBanHangsRepository.GetAll()
                        .Include(e => e.TinhFk)
                        .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false || e.ma.ToLower().Contains(input.keyword.ToLower()) || e.ten.ToLower().Contains(input.keyword.ToLower()))
                        .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue)
                        .WhereIf(!isManager, e => scope.Contains(e.ma.ToLower()));

            input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
            var pagedAndFiltered = filtered
                .OrderBy(input.Sorting ?? "id asc")
                .PageBy(input);

            var totalCount = await filtered.CountAsync();
            var results = new List<GetPhongBanHangOutput>();
            foreach (var item in pagedAndFiltered)
            {
                var output = ObjectMapper.Map<GetPhongBanHangOutput>(item);
                output.tinh = ObjectMapper.Map<TinhOutput>(item.TinhFk);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return new PagedResultDto<GetPhongBanHangOutput>(
                totalCount,
                results
            );

        }

        /// <summary>
        /// Get PhongBanHang by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/services/app/phong-ban-hang/{id}")]
        public async Task<GetPhongBanHangOutput> Get(int id)
        {
            var item = await _PhongBanHangsRepository.GetAll()
                    .Include(e => e.TinhFk)
                    .FirstOrDefaultAsync(e => e.Id == id);
            if (item == null)
            {
                throw new UserFriendlyException("id does not exist");
            }
            var output = ObjectMapper.Map<GetPhongBanHangOutput>(item);
            output.tinh = ObjectMapper.Map<TinhOutput>(item.TinhFk);
            output.tinh_trang = item.published_at.HasValue;
            return output;
        }

        /// <summary>
        /// Create or Edit PhongBanHang
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/phong-ban-hang")]
        public async Task CreateOrEdit(CreateOrEditPhongBanHangDto input)
        {
            if (input.Id == null)
            {
                await Create(input);
            }
            else
            {
                await Update(input);
            }
        }

        /// <summary>
        /// Create PhongBanHang
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Create(CreateOrEditPhongBanHangDto input)
        {
            var item = ObjectMapper.Map<PhongBanHang>(input);
            if (AbpSession.TenantId != null)
            {
                item.TenantId = (int?)AbpSession.TenantId;
            }
            if (AbpSession.UserId != null)
            {
                item.created_by_id = AbpSession.UserId;
            }
            item.created_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            await _PhongBanHangsRepository.InsertAsync(item);
        }

        /// <summary>
        /// Update PhongBanHang
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Update(CreateOrEditPhongBanHangDto input)
        {
            var item = await _PhongBanHangsRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == (int)input.Id);
            if (AbpSession.UserId != null)
            {
                item.updated_by_id = AbpSession.UserId;
            }
            item.updated_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            ObjectMapper.Map(input, item);
        }

        /// <summary>
        /// Delete PhongBanHang
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/phong-ban-hang")]
        // [HttpPost]
        public async Task Delete(EntityDto input)
        {
            await _PhongBanHangsRepository.DeleteAsync(input.Id);
        }

        /// <summary>
        /// Get all PhongBanHang for table dropdown
        /// </summary>
        /// <returns></returns>
        [Route("api/services/app/phong-ban-hang/list")]
        public async Task<List<PhongBanHangOutput>> GetAllForTableDropdown()
        {
            if (AbpSession.UserId == null)
            {
                throw new UserFriendlyException("There is no current user!");
            }
            var currentUser = await _userManager.GetUserByIdAsync(AbpSession.GetUserId());
            var roles = await _userManager.GetRolesAsync(currentUser);
            var isManager = roles.Contains("Lanh_Dao");
            var scope = currentUser.Surname.ToLower();

            return await _PhongBanHangsRepository.GetAll()
                .Where(e => e.published_at.HasValue)
                .WhereIf(!isManager, e => scope.Contains(e.ma.ToLower()))
                .OrderBy(e => e.thu_tu)
                .Select(item => new PhongBanHangOutput
                {
                    id = item.Id,
                    ma = item.ma,
                    ten = item.ten
                }).ToListAsync();
        }
    }
}