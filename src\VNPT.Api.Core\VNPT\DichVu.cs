﻿using Abp.Domain.Entities;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace VNPT.Api.Entities
{
    [Table("dich_vu", Schema = "vnpt")]
    public class DichVu : Entity, IMayHaveTenant
    {
        [Column("id")]
        public override int Id { get; set; }
        [Column("tenant_id")]
        public int? TenantId { get; set; }
        public DateTime? created_at { get; set; }
        public DateTime? updated_at { get; set; }
        public DateTime? published_at { get; set; }
        public long? created_by_id { get; set; }
        public long? updated_by_id { get; set; }
        public string ma { get; set; }
        public string ten { get; set; }
        public int? thu_tu { get; set; }
    }
}