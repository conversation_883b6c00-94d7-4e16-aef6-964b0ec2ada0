﻿using Abp.Application.Services.Dto;
using Abp.Authorization;
using Abp.Domain.Repositories;
using Abp.Linq.Extensions;
using Abp.Runtime.Session;
using Abp.UI;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using VNPT.Api.Applications.Dtos;
using VNPT.Api.Authorization;
using VNPT.Api.Authorization.Users;
using VNPT.Api.Entities;

namespace VNPT.Api.Applications
{
    /// <summary>
    /// Service for NhanVien
    /// </summary>
    [AbpAuthorize(PermissionNames.Pages_NhanVien)]
    public class NhanVienAppService : ApiAppServiceBase
    {
        private readonly UserManager _userManager;
        private readonly IRepository<NhanVien> _NhanViensRepository;
        private readonly IRepository<ThueBao> _ThueBaosRepository;

        /// <summary>
        /// Service for NhanVien
        /// </summary>
        public NhanVienAppService(
            UserManager userManager,
            IRepository<NhanVien> NhanViensRepository,
            IRepository<ThueBao> ThueBaosRepository)
        {
            _userManager = userManager;
            _NhanViensRepository = NhanViensRepository;
            _ThueBaosRepository = ThueBaosRepository;
        }

        /// <summary>
        /// Get all NhanVien
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/nhan-vien")]
        public async Task<PagedResultDto<GetNhanVienOutput>> GetAll(GetAllNhanVienInput input)
        {
            var filtered = _NhanViensRepository.GetAll()
                        .WhereIf(!string.IsNullOrWhiteSpace(input.keyword), e => false || e.ma.ToLower().Contains(input.keyword.ToLower()) || e.ho_ten.ToLower().Contains(input.keyword.ToLower()))
                        .WhereIf(input.status.HasValue, e => input.status == e.published_at.HasValue);

            input.MaxResultCount = input.MaxResultCount < 1000 ? input.MaxResultCount : 1000;
            var pagedAndFiltered = filtered
                .OrderBy(input.Sorting ?? "id asc")
                .PageBy(input);

            var totalCount = await filtered.CountAsync();
            var results = new List<GetNhanVienOutput>();
            foreach (var item in pagedAndFiltered)
            {
                var output = ObjectMapper.Map<GetNhanVienOutput>(item);
                output.tinh_trang = item.published_at.HasValue;
                results.Add(output);
            }

            return new PagedResultDto<GetNhanVienOutput>(
                totalCount,
                results
            );

        }

        /// <summary>
        /// Get NhanVien by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [Route("api/services/app/nhan-vien/{id}")]
        public async Task<GetNhanVienOutput> Get(int id)
        {
            var item = await _NhanViensRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == id);
            if (item == null)
            {
                throw new UserFriendlyException("id does not exist");
            }
            var output = ObjectMapper.Map<GetNhanVienOutput>(item);
            output.tinh_trang = item.published_at.HasValue;
            return output;
        }

        /// <summary>
        /// Create or Edit NhanVien
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/nhan-vien")]
        public async Task CreateOrEdit(CreateOrEditNhanVienDto input)
        {
            if (input.Id == null)
            {
                await Create(input);
            }
            else
            {
                await Update(input);
            }
        }

        /// <summary>
        /// Create NhanVien
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Create(CreateOrEditNhanVienDto input)
        {
            var item = ObjectMapper.Map<NhanVien>(input);
            if (AbpSession.TenantId != null)
            {
                item.TenantId = (int?)AbpSession.TenantId;
            }
            if (AbpSession.UserId != null)
            {
                item.created_by_id = AbpSession.UserId;
            }
            item.created_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            await _NhanViensRepository.InsertAsync(item);
        }

        /// <summary>
        /// Update NhanVien
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        protected virtual async Task Update(CreateOrEditNhanVienDto input)
        {
            var item = await _NhanViensRepository.GetAll()
                    .FirstOrDefaultAsync(e => e.Id == (int)input.Id);
            if (AbpSession.UserId != null)
            {
                item.updated_by_id = AbpSession.UserId;
            }
            item.updated_at = DateTime.Now;
            item.published_at = input.tinh_trang == true ? DateTime.Now : null;
            ObjectMapper.Map(input, item);
        }

        /// <summary>
        /// Delete NhanVien
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [Route("api/services/app/nhan-vien")]
        // [HttpPost]
        public async Task Delete(EntityDto input)
        {
            await _NhanViensRepository.DeleteAsync(input.Id);
        }

        /// <summary>
        /// Get all NhanVien KyThuat
        /// </summary>
        /// <returns></returns>
        [Route("api/services/app/nhan-vien/list")]
        public async Task<List<NhanVienOutput>> GetAllNhanVien(GetAllNhanVienInput input)
        {
            if (AbpSession.UserId == null)
            {
                throw new UserFriendlyException("There is no current user!");
            }
            var currentUser = await _userManager.GetUserByIdAsync(AbpSession.GetUserId());
            var roles = await _userManager.GetRolesAsync(currentUser);
            var isManager = roles.Contains("Lanh_Dao");
            var scope = currentUser.Surname.ToLower();

            if (!input.loai.HasValue)
            {
                throw new UserFriendlyException("loai is required");
            }
            if (input.loai.Value == LoaiNhanVien.KyThuat)
            {
                return await _ThueBaosRepository.GetAll()
                .WhereIf(!string.IsNullOrWhiteSpace(input.trung_tam), e => input.trung_tam.ToLower() == e.trung_tam.ToLower())
                .WhereIf(!string.IsNullOrWhiteSpace(input.phong_ban_hang), e => input.phong_ban_hang.ToLower() == e.phong_ban_hang.ToLower())
                .WhereIf(!isManager, e => scope.Contains(e.trung_tam.ToLower()))
                .GroupBy(e => e.nhan_vien_ky_thuat)
                .Select(item => new NhanVienOutput
                {
                    // id = item.Id,
                    ho_ten = item.Key,
                    trung_tam = input.trung_tam,
                    phong_ban_hang = input.phong_ban_hang
                }).ToListAsync();
            }
            else if (input.loai.Value == LoaiNhanVien.KinhDoanh)
            {
                return await _ThueBaosRepository.GetAll()
                .WhereIf(!string.IsNullOrWhiteSpace(input.trung_tam), e => input.trung_tam.ToLower() == e.trung_tam.ToLower())
                .WhereIf(!string.IsNullOrWhiteSpace(input.phong_ban_hang), e => input.phong_ban_hang.ToLower() == e.phong_ban_hang.ToLower())
                .WhereIf(!isManager, e => scope.Contains(e.trung_tam.ToLower()))
                .GroupBy(e => e.nhan_vien_kinh_doanh)
                .Select(item => new NhanVienOutput
                {
                    // id = item.Id,
                    ho_ten = item.Key,
                    trung_tam = input.trung_tam,
                    phong_ban_hang = input.phong_ban_hang
                }).ToListAsync();
            }
            else
            {
                throw new UserFriendlyException("loai is invalid");
            }

        }
    }
}